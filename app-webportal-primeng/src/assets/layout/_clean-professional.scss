/* Clean Professional Design System for PrimeNG Webportal */

/* Design Variables */
:root {
  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;

  /* Professional Color Palette */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Clean Blue Accent */
  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}

/* Clean Dashboard Layout */
.dashboard-container {
  padding: var(--space-8);
  background: var(--gray-50);
  min-height: calc(100vh - 8rem);
}

.dashboard-header {
  margin-bottom: var(--space-8);
  
  h1, h2, h3 {
    color: var(--gray-900);
    font-weight: 600;
    margin: 0;
    font-size: var(--text-3xl);
    letter-spacing: -0.025em;
  }
}

/* Clean Professional Card Styling */
.card {
  background: #ffffff;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
  }

  .card-header {
    background: #ffffff;
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-6);
    position: relative;

    .card-title {
      font-size: var(--text-lg);
      font-weight: 600;
      margin: 0 0 var(--space-1) 0;
      color: var(--gray-900);
      line-height: 1.4;
    }

    .card-subtitle {
      font-size: var(--text-sm);
      color: var(--gray-600);
      margin: 0;
      line-height: 1.4;
    }

    .card-icon {
      position: absolute;
      top: var(--space-6);
      right: var(--space-6);
      font-size: var(--text-xl);
      width: 2rem;
      height: 2rem;
      color: var(--gray-400);
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--gray-100);
      border-radius: var(--radius-md);
      transition: all 0.2s ease;
    }

    &:hover .card-icon {
      color: var(--blue-500);
      background: var(--blue-50);
    }
  }

  .card-content {
    padding: var(--space-6);
  }
}

/* Clean Stats Widget */
.stats-widget {
  .stat-item {
    background: #ffffff;
    border-radius: var(--radius-lg);
    padding: var(--space-6);
    border: 1px solid var(--gray-200);
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      box-shadow: var(--shadow-md);
      border-color: var(--gray-300);
    }

    .stat-icon {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: var(--space-4);
      transition: all 0.2s ease;

      &.blue {
        background: var(--blue-50);
        color: var(--blue-500);
      }

      &.orange {
        background: #fef3c7;
        color: #f59e0b;
      }

      &.cyan {
        background: #cffafe;
        color: #06b6d4;
      }

      &.purple {
        background: #f3e8ff;
        color: #8b5cf6;
      }

      i {
        font-size: var(--text-lg);
      }
    }

    .stat-value {
      font-size: var(--text-2xl);
      font-weight: 700;
      color: var(--gray-900);
      margin-bottom: var(--space-1);
      line-height: 1.2;
    }

    .stat-label {
      font-size: var(--text-sm);
      color: var(--gray-600);
      margin-bottom: var(--space-3);
      font-weight: 500;
    }

    .stat-change {
      font-size: var(--text-xs);
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: var(--space-1);

      &.positive {
        color: #059669;
      }

      &.negative {
        color: #dc2626;
      }

      &.neutral {
        color: var(--gray-500);
      }
    }
  }
}

/* Clean Button Styling */
.p-button {
  border-radius: var(--radius-md);
  font-weight: 500;
  font-size: var(--text-sm);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-sm);

  &:hover {
    box-shadow: var(--shadow-md);
  }

  &.p-button-outlined {
    border-color: var(--gray-300);
    color: var(--gray-700);

    &:hover {
      border-color: var(--gray-400);
      background-color: var(--gray-50);
    }
  }
}

/* Clean Form Controls */
.p-inputtext,
.p-dropdown,
.p-multiselect {
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-300);
  transition: all 0.2s ease;
  font-size: var(--text-sm);

  &:focus {
    border-color: var(--blue-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }
}

/* Clean Table Styling */
.p-datatable {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);

  .p-datatable-header {
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    padding: var(--space-4) var(--space-6);
  }

  .p-datatable-thead > tr > th {
    background: var(--gray-50);
    color: var(--gray-700);
    font-weight: 600;
    font-size: var(--text-sm);
    padding: var(--space-3) var(--space-6);
    border-bottom: 1px solid var(--gray-200);
  }

  .p-datatable-tbody > tr {
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--gray-50);
    }

    > td {
      color: var(--gray-800);
      font-size: var(--text-sm);
      padding: var(--space-3) var(--space-6);
      border-bottom: 1px solid var(--gray-100);
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-container {
    padding: var(--space-4);
  }

  .card {
    .card-header {
      padding: var(--space-4);

      .card-icon {
        top: var(--space-4);
        right: var(--space-4);
        font-size: var(--text-lg);
        width: 1.75rem;
        height: 1.75rem;
      }
    }

    .card-content {
      padding: var(--space-4);
    }
  }

  .stats-widget .stat-item {
    padding: var(--space-4);

    .stat-icon {
      width: 2rem;
      height: 2rem;
      margin-bottom: var(--space-3);

      i {
        font-size: var(--text-base);
      }
    }

    .stat-value {
      font-size: var(--text-xl);
    }
  }
}
