import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    standalone: true,
    selector: 'app-stats-widget',
    imports: [CommonModule],
    template: `<div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0 stat-item">
                <div class="stat-icon blue">
                    <i class="pi pi-shopping-cart"></i>
                </div>
                <div class="stat-value">152</div>
                <div class="stat-label">Orders</div>
                <div class="stat-change positive">
                    <i class="pi pi-arrow-up"></i>
                    <span>24 new since last visit</span>
                </div>
            </div>
        </div>
        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0 stat-item">
                <div class="stat-icon orange">
                    <i class="pi pi-dollar"></i>
                </div>
                <div class="stat-value">$2,100</div>
                <div class="stat-label">Revenue</div>
                <div class="stat-change positive">
                    <i class="pi pi-arrow-up"></i>
                    <span>52% since last week</span>
                </div>
            </div>
        </div>
        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0 stat-item">
                <div class="stat-icon cyan">
                    <i class="pi pi-users"></i>
                </div>
                <div class="stat-value">28,441</div>
                <div class="stat-label">Customers</div>
                <div class="stat-change positive">
                    <i class="pi pi-arrow-up"></i>
                    <span>520 newly registered</span>
                </div>
            </div>
        </div>
        <div class="col-span-12 lg:col-span-6 xl:col-span-3">
            <div class="card mb-0 stat-item">
                <div class="stat-icon purple">
                    <i class="pi pi-comment"></i>
                </div>
                <div class="stat-value">152</div>
                <div class="stat-label">Unread Comments</div>
                <div class="stat-change neutral">
                    <i class="pi pi-minus"></i>
                    <span>85 responded</span>
                </div>
            </div>
        </div>`
})
export class StatsWidget {}
