<div class="overlay" [class.disabled]="isLoading">
  <app-details-header
    [withRefresh]="true"
    [writeRight]="UserRight.CustomersWrite"
    [labelExist]="'SUBSCRIPTIONS.DETAILS'"
    [modelKey]="this.modelKey"
    [isEditButtonReadOnly]="!isEditable"
    (onGoBack)="goBack()"
    (onEditClick)="editSubscription()"
    (onRefresh)="reloadModels()"
  >
    @if (this.sessionService.hasRight(UserRight.CustomersWrite)) {
      <div>
        <button
          mat-stroked-button
          [disabled]="!canActivateSubscription()"
          (click)="onActivateSubscription()"
          style="margin-right: 6px"
          *ngIf="canActivateSubscription()"
        >
          <mat-icon class="colored-mat-icon">play_arrow</mat-icon>
          {{ 'SUBSCRIPTIONS.ACTIVATE' | translate }}
        </button>
        <button
          mat-stroked-button
          [disabled]="!canPauseSubscription()"
          (click)="onPauseSubscription()"
          style="margin-right: 6px"
          *ngIf="canPauseSubscription()"
        >
          <mat-icon class="colored-mat-icon">pause</mat-icon>
          {{ 'SUBSCRIPTIONS.PAUSE' | translate }}
        </button>
        <button
          mat-stroked-button
          [disabled]="!isCancelable"
          (click)="onCancelSubscription()"
          *ngIf="model?.subscriptionData?.status !== 'CANCELLED'"
        >
          <mat-icon class="colored-mat-icon">cancel</mat-icon>
          {{ 'SUBSCRIPTIONS.CANCEL' | translate }}
        </button>
      </div>
    }
  </app-details-header>

  <!-- Subscription Information Section -->
  <mat-card>
    <mat-card-content class="card-container">
      <mat-card class="subscription-details-card" style="margin-right: 16px">
        <mat-card-header>
          <mat-card-title>{{ 'MAIN.DETAILS' | translate }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.KEY' | translate }}:</label>
            <span>{{ model?.subscriptionData?.key }}</span>
          </div>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.NAME' | translate }}:</label>
            <span>{{ model?.subscriptionData?.name }}</span>
          </div>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.STATUS' | translate }}:</label>
            <span class="status-badge" [ngClass]="'status-' + model?.subscriptionData?.status?.toLowerCase()">
              {{ 'SUBSCRIPTION_STATUS.' + model?.subscriptionData?.status | translate }}
            </span>
          </div>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.FREQUENCY' | translate }}:</label>
            <span>{{ 'SUBSCRIPTION_FREQUENCY.' + model?.subscriptionData?.frequency | translate }}</span>
          </div>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.AMOUNT' | translate }}:</label>
            <span class="amount">{{ model?.subscriptionData?.amount | currency:model?.subscriptionData?.currency }}</span>
          </div>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.START_DATE' | translate }}:</label>
            <span>{{ model?.subscriptionData?.startDate | date }}</span>
          </div>
          <div class="form-row" *ngIf="model?.subscriptionData?.endDate">
            <label>{{ 'SUBSCRIPTIONS.END_DATE' | translate }}:</label>
            <span>{{ model?.subscriptionData?.endDate | date }}</span>
          </div>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.NEXT_BILLING_DATE' | translate }}:</label>
            <span>{{ model?.subscriptionData?.nextBillingDate | date }}</span>
          </div>
          <div class="form-row" *ngIf="model?.subscriptionData?.description">
            <label>{{ 'SUBSCRIPTIONS.DESCRIPTION' | translate }}:</label>
            <span>{{ model?.subscriptionData?.description }}</span>
          </div>
          <div class="form-row">
            <label>{{ 'SUBSCRIPTIONS.BUSINESS_SEGMENT' | translate }}:</label>
            <a [routerLink]="['/business-segments', model?.subscriptionData?.businessSegmentKey]">{{
                model?.subscriptionData?.businessSegmentKey
              }}</a>
          </div>
          <div class="form-row" *ngIf="model?.subscriptionData?.customerKey">
            <label>{{ 'CUSTOMERS.CUSTOMER' | translate }}:</label>
            <span class="with-icon">
              <mat-icon>person</mat-icon>
              <a [routerLink]="['/customers', model?.subscriptionData?.customerKey]">{{ model?.subscriptionData?.customerKey }}</a>
            </span>
          </div>
        </mat-card-content>
      </mat-card>
      @if (billingAddress) {
        <mat-card class="address-card" style="margin-right: 16px">
          <mat-card-header>
            <mat-card-title>{{ 'CUSTOMERS.BILLING_ADDRESS' | translate }}</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            @if (customerData?.customerType === 'COMPANY') {
              <p>{{ billingAddress?.companyName }}</p>
            } @else {
              <p>
                {{ billingAddress?.lastName }},
                {{ billingAddress?.firstName }}
              </p>
            }
            <p>
              {{ billingAddress?.street }}
              {{ billingAddress?.houseNumber }}
            </p>
            <p>
              {{ billingAddress?.city }},
              {{ billingAddress?.country }}
              {{ billingAddress?.postalCode }}
            </p>
            @if (billingAddress?.mailAddress) {
              <p>
                {{ billingAddress?.mailAddress }}
              </p>
            }
          </mat-card-content>
        </mat-card>
      }
      @if (shippingAddress) {
        <mat-card class="address-card">
          <mat-card-header>
            <mat-card-title>{{ 'CUSTOMERS.SHIPPING_ADDRESS' | translate }}</mat-card-title>
          </mat-card-header>
          <mat-card-content>
            @if (customerData?.customerType === 'COMPANY') {
              <p>{{ shippingAddress?.companyName }}</p>
            } @else {
              <p>
                {{ shippingAddress?.lastName }},
                {{ shippingAddress?.firstName }}
              </p>
            }
            <p>
              {{ shippingAddress?.street }}
              {{ shippingAddress?.houseNumber }}
            </p>
            <p>
              {{ shippingAddress?.city }},
              {{ shippingAddress?.country }}
              {{ shippingAddress?.postalCode }}
            </p>
          </mat-card-content>
        </mat-card>
      }
    </mat-card-content>
  </mat-card>

  <span style="display: block; height: 5px"></span>

  <!-- Subscription Items Section -->
  <mat-expansion-panel expanded="true">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <mat-card-title>{{ 'SUBSCRIPTIONS.ITEMS' | translate }}</mat-card-title>
      </mat-panel-title>
    </mat-expansion-panel-header>

    @if (this.sessionService.hasRight(UserRight.OrdersRead)) {
      <app-details-table
        #tableSubscriptionItems
        [columns]="subscriptionItemsColumns"
        initialSort="totalNetAmount"
        initialSortDirection="desc"
        (loadDataEmitter)="loadSubscriptionItems($event)"
        [exportHeader]="'SUBSCRIPTIONS.ITEMS' | translate"
      ></app-details-table>
    }
  </mat-expansion-panel>

  <span style="display: block; height: 5px"></span>

  @if (this.sessionService.hasRight(UserRight.OpenItemsRead)) {
    <mat-expansion-panel expanded="true">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <mat-card-title>{{ 'ORDERS.BALANCE_INFORMATION' | translate }}</mat-card-title>
        </mat-panel-title>
      </mat-expansion-panel-header>

      <app-details-table
        #tableBalanceInformation
        [columns]="balanceInformationColumns"
        initialSort="postingDate"
        initialSortDirection="desc"
        (loadDataEmitter)="loadBalanceInformation($event)"
        [exportHeader]="'ORDERS.BALANCE_INFORMATION' | translate"
      ></app-details-table>

      @if (balanceCaseData) {
        <div style="display: flex; justify-content: flex-end">
          <h4>
            <table>
              <tr>
                <td>{{ 'ORDERS.TOTAL_DEBIT' | translate }}:</td>
                <td style="text-align: right">
                  <app-money
                    [amount]="balanceCaseData.totalDebitAmount"
                    [currency]="balanceCaseData.currency"
                  />
                </td>
              </tr>
              <tr>
                <td>{{ 'ORDERS.TOTAL_CREDIT' | translate }}:</td>
                <td style="text-align: right">
                  <app-money
                    [amount]="balanceCaseData.totalCreditAmount"
                    [currency]="balanceCaseData.currency"
                  />
                </td>
              </tr>
              <tr>
                <td>{{ 'ORDERS.OPEN_AMOUNT' | translate }}:</td>
                <td style="text-align: right">
                  <app-money
                    [amount]="balanceCaseData.totalBalanceAmount"
                    [currency]="balanceCaseData.currency"
                  />
                </td>
              </tr>
            </table>
          </h4>
        </div>
      }
    </mat-expansion-panel>

    <span style="display: block; height: 5px"></span>
  }

  <!-- Tabs for detailed information -->
  <mat-expansion-panel expanded="true">
    <mat-tab-group>
      <!-- Documents Tab -->
      @if (this.sessionService.hasRight(UserRight.DocumentsRead)) {
        <mat-tab [label]="'SUBSCRIPTIONS.DOCUMENTS' | translate">
          <app-details-table
            #tableDocuments
            [columns]="documentsColumns"
            initialSort="key"
            initialSortDirection="desc"
            (loadDataEmitter)="loadDocuments($event)"
            [exportHeader]="'SUBSCRIPTIONS.DOCUMENTS' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- Posting Records Tab -->
      @if (this.sessionService.hasRight(UserRight.PostingRecordsRead)) {
        <mat-tab [label]="'POSTING_RECORDS.TITLE' | translate">
          <app-details-table
            #tablePostingRecords
            [columns]="postingRecordsColumns"
            initialSort="bookingDate"
            initialSortDirection="desc"
            (loadDataEmitter)="loadPostingRecords($event)"
            [exportHeader]="'POSTING_RECORDS.TITLE' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- Notes Tab -->
      @if (this.sessionService.hasRight(UserRight.OrdersRead)) {
        <mat-tab [label]="'NOTES.NOTES' | translate">
          <app-details-table
            #tableNotes
            [columns]="notesColumns"
            initialSort="createdAt"
            initialSortDirection="desc"
            [withAdd]=true
            (loadDataEmitter)="loadNotes($event)"
            (addDataEmitter)="onAddNote()"
            [exportHeader]="'NOTES.NOTES' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- History Tab -->
      @if (this.sessionService.hasRight(UserRight.OrdersRead)) {
        <mat-tab [label]="'SUBSCRIPTIONS.HISTORY' | translate">
          <app-details-table
            #tableHistory
            [columns]="historyColumns"
            initialSort="number"
            initialSortDirection="desc"
            (loadDataEmitter)="loadHistory($event)"
            [exportHeader]="'SUBSCRIPTIONS.HISTORY' | translate"
          ></app-details-table>
        </mat-tab>
      }

      <!-- Properties Tab -->
      <mat-tab [label]="'MAIN.PROPERTIES' | translate">
        <app-details-table
          #tableProperties
          [columns]="propertiesColumns"
          initialSort="key"
          initialSortDirection="asc"
          (loadDataEmitter)="loadProperties($event)"
          [exportHeader]="'MAIN.PROPERTIES' | translate"
        ></app-details-table>
      </mat-tab>
    </mat-tab-group>
  </mat-expansion-panel>
</div>
