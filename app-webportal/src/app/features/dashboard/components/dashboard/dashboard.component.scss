@use '@angular/material' as mat;

@include mat.core();

.dashboard-container {
  padding: var(--space-8);
  min-height: calc(100vh - 64px);
  background: var(--gray-50);
}

.dashboard-header {
  margin-bottom: 32px;

  h3 {
    font-size: var(--text-3xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: 0;
    letter-spacing: -0.025em;
  }
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  grid-auto-rows: minmax(320px, auto);
  gap: 24px;
  margin-bottom: 24px;
  width: 100%;
  box-sizing: border-box;

  .dashboard-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
    min-height: 320px;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
    overflow: hidden;

    &:nth-child(1) { animation-delay: 0.1s; }
    &:nth-child(2) { animation-delay: 0.2s; }
    &:nth-child(3) { animation-delay: 0.3s; }
    &:nth-child(4) { animation-delay: 0.4s; }
    &:nth-child(5) { animation-delay: 0.5s; }
    &:nth-child(6) { animation-delay: 0.6s; }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-card {
  background: #ffffff;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  transition: all 0.2s ease;
  overflow: hidden;

  &:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--gray-300);
  }
}

.card-header {
  padding: var(--space-6);
  background: #ffffff;
  border-bottom: 1px solid var(--gray-200);
  position: relative;

  .card-title {
    font-size: var(--text-lg);
    font-weight: 600;
    margin: 0 0 var(--space-1) 0;
    color: var(--gray-900);
    line-height: 1.4;
  }

  .card-subtitle {
    font-size: var(--text-sm);
    color: var(--gray-600);
    margin: 0;
    line-height: 1.4;
  }

  .card-icon {
    position: absolute;
    top: var(--space-6);
    right: var(--space-6);
    font-size: var(--text-xl);
    width: 2rem;
    height: 2rem;
    color: var(--gray-400);
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: var(--radius-md);
    transition: all 0.2s ease;
  }

  &:hover .card-icon {
    color: var(--blue-500);
    background: var(--blue-50);
  }
}

.card-content {
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: 0;
  flex: 1;
  min-height: 200px;
  box-sizing: border-box;
  overflow: hidden;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--dashboard-metric-value-color, #1e40af);
  margin: 0 0 8px 0;
  line-height: 1;
}

.metric-label {
  font-size: 0.875rem;
  color: var(--dashboard-metric-label-color, #6b7280);
  margin: 0 0 16px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.metric-change {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;

  &.positive {
    background: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }

  &.negative {
    background: rgba(239, 68, 68, 0.1);
    color: #ef4444;
  }

  &.neutral {
    background: rgba(107, 114, 128, 0.1);
    color: #6b7280;
  }
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  margin-top: 8px;
  width: 100%;
}

.metric-item {
  text-align: center;
  padding: 20px 16px;
  background: rgba(30, 64, 175, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(30, 64, 175, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &:hover {
    background: rgba(30, 64, 175, 0.1);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(30, 64, 175, 0.15);
  }

  .metric-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e40af;
    margin: 0 0 4px 0;
  }

  .metric-text {
    font-size: 0.75rem;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-weight: 500;
  }

  &:hover .metric-number {
    color: #1d4ed8;
  }

  .metric-change {
    display: inline-flex;
    align-items: center;
    gap: 2px;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 0.65rem;
    font-weight: 600;
    margin-top: 4px;

    &.positive {
      background: rgba(16, 185, 129, 0.1);
      color: #10b981;
    }

    &.negative {
      background: rgba(239, 68, 68, 0.1);
      color: #ef4444;
    }

    &.neutral {
      background: rgba(107, 114, 128, 0.1);
      color: #6b7280;
    }

    mat-icon {
      font-size: 0.75rem;
      width: 0.75rem;
      height: 0.75rem;
    }
  }
}

.echart-diagram {
  width: 100% !important;
  height: 250px !important;
  border-radius: 8px;
  min-width: 0;
  flex: 1;
  min-height: 200px;
  max-width: 100%;
  box-sizing: border-box;
}

.task-list {
  list-style: none;
  padding: 0;
  margin: 0;

  .task-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);

    &:last-child {
      border-bottom: none;
    }

    .task-info {
      flex: 1;

      .task-name {
        font-weight: 500;
        color: #374151;
        margin: 0 0 4px 0;
      }

      .task-date {
        font-size: 0.75rem;
        color: #6b7280;
      }
    }

    .task-amount {
      font-weight: 600;
      color: #1e40af;
    }
  }
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6b7280;

  mat-icon {
    font-size: 3rem;
    width: 3rem;
    height: 3rem;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  p {
    margin: 0;
    font-size: 0.875rem;
  }
}

// Responsive design
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    grid-auto-rows: minmax(280px, auto);
    gap: 16px;

    .dashboard-card {
      min-height: 280px;
      width: 100%;
      max-width: 100%;
    }
  }

  .card-header {
    padding: 14px 16px 10px;

    .card-title {
      font-size: 1rem;
    }

    .card-icon {
      right: 16px;
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
      top: 14px;
    }
  }

  .card-content {
    padding: 16px;
    min-height: 180px;
  }

  .metric-value {
    font-size: 2rem;
  }

  .echart-diagram {
    height: 200px !important;
    min-height: 150px;
  }

  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 12px;
  }
}

.clickable-metric {
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.1);
  }
}
