/* Clean Professional Design System */

:root {
  /* Spacing Scale */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* Professional Color Palette */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Clean Blue Accent */
  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;

  /* Typography */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background-color: #fafbfc;
  color: #374151;
  line-height: 1.5;
  font-size: 14px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.loading-spinner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.form-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  max-width: 400px;
}

.form-row label {
  width: 200px;
}

.form-row span {
  text-align: right;
}

.form-row a {
  text-align: right;
}

.mat-mdc-card-header {
  margin-bottom: 12px;
}

.with-icon {
  display: flex;
  align-items: center;
}

.with-icon > * {
  margin-right: 5px;
}

/* Scrollbar */

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: lightgrey;
}

::-webkit-scrollbar-thumb:hover {
  background: lightgrey;
}

.back-button {
  margin-left: -10px;
}

.mat-mdc-row {
  height: 38px !important;
}

.vertical-form {
  display: flex;
  flex-direction: column;
}

.mat-mdc-form-field {
  margin-bottom: 14px;
}

.mdc-tab__text-label {
  font-size: 18px;
}

.mat-mdc-tab {
  margin-top: 8px !important;
  margin-bottom: 12px !important;
  font-weight: bold;
}

.overlay {
  width: 100%;
  height: 100%;
  z-index: 9900;
}

.overlay.disabled {
  pointer-events: none;
  z-index: 9998;
  opacity: 0.4;
}

.spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

/* BPMN Properties Panel Global Styles */
.bio-properties-panel {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  font-size: 14px !important; /* Vergrößert von 12px auf 14px */
  background: white !important;
  border: none !important;
  padding-bottom: 70px !important;
  min-height: 100% !important;
}

/* AI Chat Styles für Properties Panel */
.ai-chat-container {
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  background: #f8f9fa !important;
  border-radius: 8px !important;
  padding: 12px !important;
  margin: 8px 0 !important;
}

.ai-chat-messages {
  max-height: 300px !important;
  overflow-y: auto !important;
  margin-bottom: 12px !important;
  background: white !important;
  border-radius: 6px !important;
  padding: 8px !important;
  border: 1px solid #e0e0e0 !important;
}

.ai-message {
  display: flex !important;
  margin-bottom: 12px !important;
  align-items: flex-start !important;
}

.ai-message.user {
  flex-direction: row-reverse !important;
}

.ai-message.user .message-content {
  background: #1976d2 !important;
  color: white !important;
  margin-right: 8px !important;
  margin-left: 0 !important;
}

.ai-message.ai .message-content,
.ai-message.system .message-content {
  background: #f5f5f5 !important;
  color: #333 !important;
  margin-left: 8px !important;
  margin-right: 0 !important;
}

.message-avatar {
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 14px !important;
  flex-shrink: 0 !important;
}

.message-content {
  padding: 8px 12px !important;
  border-radius: 12px !important;
  font-size: 13px !important; /* Vergrößert von 12px auf 13px */
  line-height: 1.4 !important;
  max-width: 200px !important;
  word-wrap: break-word !important;
}

.ai-quick-actions {
  display: flex !important;
  gap: 6px !important;
  margin-bottom: 12px !important;
  flex-wrap: wrap !important;
}

.ai-quick-btn {
  background: #e3f2fd !important;
  border: 1px solid #1976d2 !important;
  color: #1976d2 !important;
  padding: 6px 10px !important; /* Vergrößert padding */
  border-radius: 16px !important;
  font-size: 11px !important; /* Vergrößert von 10px auf 11px */
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  white-space: nowrap !important;
}

.ai-quick-btn:hover {
  background: #1976d2 !important;
  color: white !important;
}

.ai-chat-input-area {
  border-top: 1px solid #e0e0e0 !important;
  padding-top: 8px !important;
}

.ai-input-container {
  display: flex !important;
  gap: 6px !important;
  align-items: flex-end !important;
}

.ai-chat-input {
  flex: 1 !important;
  padding: 8px 10px !important; /* Vergrößert padding */
  border: 1px solid #ccc !important;
  border-radius: 6px !important;
  font-size: 13px !important; /* Vergrößert von 12px auf 13px */
  font-family: "Helvetica Neue", Helvetica, Arial, sans-serif !important;
  resize: vertical !important;
  min-height: 24px !important; /* Vergrößert von 20px auf 24px */
}

.ai-chat-input:focus {
  outline: none !important;
  border-color: #1976d2 !important;
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2) !important;
}

.ai-send-btn {
  background: #1976d2 !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  padding: 6px 8px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  transition: background 0.2s ease !important;
}

.ai-send-btn:hover {
  background: #1565c0 !important;
}

/* ===== CLEAN PROFESSIONAL DESIGN SYSTEM ===== */

/* Clean Professional Card Styling */
.mat-mdc-card {
  background: #ffffff !important;
  box-shadow: var(--shadow-sm) !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--gray-200) !important;
  transition: box-shadow 0.2s ease, border-color 0.2s ease !important;

  &:hover {
    box-shadow: var(--shadow-md) !important;
    border-color: var(--gray-300) !important;
  }
}

/* Clean Button Styling */
.mat-mdc-raised-button {
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  text-transform: none !important;
  font-size: var(--text-sm) !important;
  padding: var(--space-2) var(--space-4) !important;
  box-shadow: var(--shadow-sm) !important;
  transition: all 0.2s ease !important;

  &:hover {
    box-shadow: var(--shadow-md) !important;
  }
}

.mat-mdc-outlined-button {
  border-radius: var(--radius-md) !important;
  font-weight: 500 !important;
  text-transform: none !important;
  font-size: var(--text-sm) !important;
  border-color: var(--gray-300) !important;
  color: var(--gray-700) !important;

  &:hover {
    border-color: var(--gray-400) !important;
    background-color: var(--gray-50) !important;
  }
}

/* Clean Form Styling */
.mat-mdc-form-field {
  .mat-mdc-text-field-wrapper {
    border-radius: var(--radius-md) !important;
    background-color: #ffffff !important;
    border: 1px solid var(--gray-300) !important;
    transition: border-color 0.2s ease !important;

    &:hover {
      border-color: var(--gray-400) !important;
    }

    &.mdc-text-field--focused {
      border-color: var(--blue-500) !important;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    }
  }

  .mat-mdc-form-field-label {
    color: var(--gray-600) !important;
    font-size: var(--text-sm) !important;
    font-weight: 500 !important;
  }

  .mat-mdc-input-element {
    color: var(--gray-800) !important;
    font-size: var(--text-sm) !important;
  }
}

/* Clean Table Styling */
.mat-mdc-table {
  background: #ffffff !important;
  border-radius: var(--radius-lg) !important;
  border: 1px solid var(--gray-200) !important;
  overflow: hidden !important;

  .mat-mdc-header-row {
    background-color: var(--gray-50) !important;
    border-bottom: 1px solid var(--gray-200) !important;

    .mat-mdc-header-cell {
      color: var(--gray-700) !important;
      font-weight: 600 !important;
      font-size: var(--text-sm) !important;
      padding: var(--space-3) var(--space-4) !important;
    }
  }

  .mat-mdc-row {
    border-bottom: 1px solid var(--gray-100) !important;
    transition: background-color 0.2s ease !important;

    &:hover {
      background-color: var(--gray-50) !important;
    }

    .mat-mdc-cell {
      color: var(--gray-800) !important;
      font-size: var(--text-sm) !important;
      padding: var(--space-3) var(--space-4) !important;
    }
  }
}

.ai-context-info {
  margin-top: 4px !important;
  color: #666 !important;
  font-size: 10px !important;
}

.ai-status {
  background: #fff3e0 !important;
  border: 1px solid #ffb74d !important;
  border-radius: 6px !important;
  padding: 8px !important;
  margin-top: 8px !important;
}

.ai-thinking {
  display: flex !important;
  align-items: center !important;
  gap: 6px !important;
  font-size: 12px !important; /* Vergrößert von 11px auf 12px */
  color: #f57c00 !important;
}

.ai-spinner {
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Scrollbar für AI Chat */
.ai-chat-messages::-webkit-scrollbar {
  width: 4px !important;
}

.ai-chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
}

.ai-chat-messages::-webkit-scrollbar-thumb {
  background: #ccc !important;
  border-radius: 2px !important;
}

.ai-chat-messages::-webkit-scrollbar-thumb:hover {
  background: #999 !important;
}
