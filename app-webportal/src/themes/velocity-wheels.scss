@use '@angular/material' as mat;
@use '../app/_app.component-theme.scss' as app;

@include mat.core();

$velocity-wheels-brand: (
  50: #f0f9ff,
  100: #e0f2fe,
  200: #bae6fd,
  300: #7dd3fc,
  400: #38bdf8,
  500: #0284c7,  // Enhanced darker blue for better readability
  600: #0369a1,  // Deeper blue for better contrast
  700: #0c4a6e,  // Dark blue for headers and important elements
  800: #075985,  // Very dark blue
  900: #0a2e4a,  // Darkest blue for maximum contrast
  A100: #e0f2fe,
  A200: #7dd3fc,
  A400: #0284c7,
  A700: #0369a1,
  contrast: (
    50: rgba(black, 0.87),
    100: rgba(black, 0.87),
    200: rgba(black, 0.87),
    300: rgba(black, 0.87),
    400: white,
    500: white,
    600: white,
    700: white,
    800: white,
    900: white,
    A100: rgba(black, 0.87),
    A200: rgba(black, 0.87),
    A400: white,
    A700: white,
  )
);

$primary: mat.m2-define-palette($velocity-wheels-brand, 500);
$accent: mat.m2-define-palette(mat.$m2-amber-palette, A200, A100, A400);

$theme: mat.m2-define-light-theme((
  color: (
    primary: $primary,
    accent: $accent,
  ),
  typography: mat.m2-define-typography-config(),
  density: -3,
));

$primary-default: mat.get-theme-color($theme, primary, default);

@include mat.all-component-themes($theme);

@include mat.button-density(0);
@include app.theme($theme);
