package com.klosesoft.billingsolution.databasefiller.tenant

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_ID
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_KEY
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_BIKESALE_UK
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.databasefiller.DomainUtil
import com.klosesoft.billingsolution.databasefiller.creator.BookkeepingCreator
import com.klosesoft.billingsolution.databasefiller.creator.CustomerCreator
import com.klosesoft.billingsolution.databasefiller.creator.NoteCreator
import com.klosesoft.billingsolution.databasefiller.creator.NotificationReceiverCreator
import com.klosesoft.billingsolution.databasefiller.creator.OrderCreator
import com.klosesoft.billingsolution.databasefiller.creator.PaymentCreator
import com.klosesoft.billingsolution.databasefiller.creator.ReportConfigCreator
import com.klosesoft.billingsolution.databasefiller.creator.SupplierCreator
import com.klosesoft.billingsolution.databasefiller.creator.TenantCreator
import com.klosesoft.billingsolution.databasefiller.creator.TranslationCreator
import com.klosesoft.billingsolution.domain.logic.api.job.report.ReportJob
import com.klosesoft.billingsolution.domain.logic.api.service.WorkflowApiService
import com.klosesoft.billingsolution.domain.logic.service.OrderPaymentTransactionService
import com.klosesoft.billingsolution.domain.logic.service.PaymentTransactionService
import com.klosesoft.billingsolution.domain.logic.service.billing.TranslationService
import com.klosesoft.billingsolution.domain.logic.service.billing.mapping.BikeSaleUkDocumentToPdfMapping
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingAccountService
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingRuleService
import com.klosesoft.billingsolution.domain.logic.service.common.AddressService
import com.klosesoft.billingsolution.domain.logic.service.common.BusinessSegmentService
import com.klosesoft.billingsolution.domain.logic.service.common.CustomerService
import com.klosesoft.billingsolution.domain.logic.service.common.NoteService
import com.klosesoft.billingsolution.domain.logic.service.common.NotificationApiReceiverService
import com.klosesoft.billingsolution.domain.logic.service.common.SupplierService
import com.klosesoft.billingsolution.domain.logic.service.common.TenantService
import com.klosesoft.billingsolution.domain.logic.service.common.WorkflowDefinitionService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentAccountService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentCategorizationRuleService
import com.klosesoft.billingsolution.domain.logic.service.user.RoleService
import com.klosesoft.billingsolution.domain.logic.service.user.UserService
import com.klosesoft.billingsolution.domain.model.dto.AddressDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderItemRequestDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderSupplierRequestDomainDto
import com.klosesoft.billingsolution.domain.model.dto.PaymentAssignmentDomainDto
import com.klosesoft.billingsolution.domain.model.dto.SupplyType
import com.klosesoft.billingsolution.domain.model.dto.TaxInformationDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.AddressType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingRuleType
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.domain.model.valueobject.FinancingType
import com.klosesoft.billingsolution.domain.model.valueobject.GeneralDocumentTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.Money
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentOrigin
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentStatus
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentTargetType
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentType
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentCategory
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentTransactionAssignmentStatus
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import com.klosesoft.billingsolution.domain.model.valueobject.ReportFormat
import com.klosesoft.billingsolution.domain.model.valueobject.ReportInterval
import com.klosesoft.billingsolution.domain.model.valueobject.ReportType
import com.klosesoft.billingsolution.domain.model.valueobject.Right
import com.klosesoft.billingsolution.domain.model.valueobject.TargetType
import com.klosesoft.billingsolution.domain.model.valueobject.TaxCode
import com.klosesoft.billingsolution.persistence.model.entity.Customer
import com.klosesoft.billingsolution.persistence.model.entity.PaymentTransaction
import com.klosesoft.billingsolution.persistence.model.entity.Role
import com.klosesoft.billingsolution.persistence.service.AddressLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.OrderLoadService
import com.klosesoft.billingsolution.persistence.service.RoleLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UniqueIdGeneratorService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import com.klosesoft.billingsolution.workflow.WorkflowDeploymentService
import org.springframework.core.env.Environment
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Component
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate
import java.util.EnumMap
import java.util.Locale
import java.util.UUID

@Component
class BikeSaleUkInitializer(
    private val tenantService: TenantService,
    private val tenantLoadService: TenantLoadService,
    private val customerLoadService: CustomerLoadService,
    private val orderLoadService: OrderLoadService,
    private val orderPaymentTransactionService: OrderPaymentTransactionService,
    private val businessSegmentService: BusinessSegmentService,
    private val userService: UserService,
    private val userLoadService: UserLoadService,
    private val customerService: CustomerService,
    private val workflowApiService: WorkflowApiService,
    private val addressLoadService: AddressLoadService,
    private val paymentTransactionService: PaymentTransactionService,
    private val notificationApiReceiverService: NotificationApiReceiverService,
    private val environment: Environment,
    private val bookingAccountService: BookingAccountService,
    private val bookingRuleService: BookingRuleService,
    private val roleService: RoleService,
    private val roleLoadService: RoleLoadService,
    private val uniqueIdGeneratorService: UniqueIdGeneratorService,
    private val paymentAccountService: PaymentAccountService,
    private val paymentCategorizationRuleService: PaymentCategorizationRuleService,
    private val reportConfigCreator: ReportConfigCreator,
    private val reportJob: ReportJob,
    private val translationService: TranslationService,
    private val supplierService: SupplierService,
    private val resourceLoader: ResourceLoader,
    private val addressService: AddressService,
    private val noteService: NoteService,
    private val workflowDeploymentService: WorkflowDeploymentService,
    private val workflowDefinitionService: WorkflowDefinitionService,
) {

    private companion object {
        const val CUBE_CASH = "CUBE_CASH"
        const val CUBE_FINANCED = "CUBE_FINANCED"
        const val CANYON_CASH = "CANYON_CASH"
        const val CANYON_FINANCED = "CANYON_FINANCED"
    }

    suspend fun setupTenant() {
        createTenant()
        createBusinessSegments()
        createTenantWebPortalUsers()
        createBookingkeepingConfig()
        createPaymentConfig()
        createTranslations()

        if (environment.activeProfiles.contains("demo")) {
            addCustomers()
            addSuppliers()
            addOrders()
            addNotes()
            addPaymentTransactions()
            createReports()
        }

        if (environment.activeProfiles.contains("cucumber")) {
            createCucumberNotificationApiReceiver()
        }
    }

    private suspend fun createReports() {
        val bikeSaleUKTenantId = tenantLoadService.findTenantByKey(TENANT_KEY_BIKESALE_UK).id
        reportConfigCreator.createConfig(bikeSaleUKTenantId, ReportType.PAYMENT, ReportFormat.JSON, ReportInterval.DAILY, "UTC")
        reportConfigCreator.createConfig(bikeSaleUKTenantId, ReportType.SUBLEDGER, ReportFormat.JSON, ReportInterval.DAILY, "UTC")

        reportJob.generateSubledgerReport()
        reportJob.generatePaymentReport()
        reportJob.generateApprovalReport()
    }

    private suspend fun createTenantWebPortalUsers() {
        val bikeSaleUKTenantId = tenantLoadService.findTenantByKey(TENANT_KEY_BIKESALE_UK).id

        createRoles(bikeSaleUKTenantId)
        createUserRoles(bikeSaleUKTenantId)
        createRoleRights(bikeSaleUKTenantId)
    }

    private suspend fun createRoles(
        bikeSaleUKTenantId: UUID,
    ) {
        roleService.createRole(createRole("Read-only", bikeSaleUKTenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Full Access", bikeSaleUKTenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Administration", bikeSaleUKTenantId), emptyList(), SYSTEM_USER_ID)
    }

    private suspend fun createRole(
        name: String,
        tenantId: UUID,
    ) = Role(
        name = name,
        createdBy = SYSTEM_USER_ID,
        lastModifiedBy = SYSTEM_USER_ID,
        key = name,
        tenantId = tenantId,
    )

    private suspend fun createUserRoles(
        tenantId: UUID,
    ) {
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)

        setRolesForUser("<EMAIL>", listOf("Read-only"), tenantId)
    }

    private suspend fun setRolesForUser(
        key: String,
        roleKeys: List<String>,
        bikeSaleUKTenantId: UUID,
    ) {
        val user = userLoadService.findByKey(key)

        val roles = mutableListOf<Role>()

        roleKeys.forEach { roleKey ->
            val role = roleLoadService.findByTenantIdAndKey(bikeSaleUKTenantId, roleKey)
            roles.add(role)
        }

        userService.updateUser(user, roles, SYSTEM_USER_ID)
    }

    private suspend fun createRoleRights(
        tenantId: UUID,
    ) {
        addReadOnlyRights(tenantId)
        addFullAccessRights(tenantId)
        addAdminRights(tenantId)
    }

    private suspend fun addReadOnlyRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Read-only")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addFullAccessRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Full Access")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ || it.category == Right.Category.WRITE
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addAdminRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Administration")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.ADMIN || it.category == Right.Category.INTERNAL
            },
            SYSTEM_USER_ID,
        )
    }

    private fun getLogo(): ByteArray {
        val logoResource = resourceLoader.getResource("classpath:/logos/logo-bikesale-uk.png")

        if (logoResource.exists()) {
            try {
                return logoResource.contentAsByteArray
            } catch (e: IOException) {
                throw IllegalStateException("error while reading logo", e)
            }
        } else {
            throw IllegalStateException("logo was not found")
        }
    }

    private suspend fun createBusinessSegments() {
        val logo = getLogo()

        val originatorAddress = addressService.createAddress(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            AddressDomainDto(
                UUID.randomUUID().toString(),
                companyName = "BikeSale UK Ltd.",
                street = "Woodberry Grove",
                houseNumber = "15",
                city = "London",
                country = "UK",
                postalCode = "1234",
                mailAddress = "",
            ),
        )

        val tenantIdBikeSaleUK = tenantLoadService.findTenantByKey(TENANT_KEY_BIKESALE_UK).id
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantIdBikeSaleUK,
                CUBE_CASH,
                "'07'&\$yearShort&'0102'&\$numberRange(1,9999999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB123456789",
            ),
        )
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantIdBikeSaleUK,
                CUBE_FINANCED,
                "'07'&\$yearShort&'0101'&\$numberRange(1,9999999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB123456789",
            ),
        )
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantIdBikeSaleUK,
                CANYON_CASH,
                "'07'&\$yearShort&'0802'&\$numberRange(1,9999999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB123456789",
            ),
        )
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantIdBikeSaleUK,
                CANYON_FINANCED,
                "'07'&\$yearShort&'0801'&\$numberRange(1,9999999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "GB123456789",
            ),
        )
    }

    protected suspend fun createTenant() {
        val tenantBikeSaleUk = tenantService.createTenant(TenantCreator.createActiveTenant(TENANT_KEY_BIKESALE_UK))
        tenantService.createTenantConfig(
            TenantCreator.createTenantConfig(
                tenantId = tenantBikeSaleUk.id,
                appClientId = "3rth10k720ihm8ip646j5nvv79",
                orderWorkflow = BillingSolutionConstants.BIKESALE_UK_WORKFLOW,
                ledgerCurrency = Currency.GBP,
                features = listOf(Feature.ENABLE_BALANCE_CASE),
                locale = Locale.UK,
                theme = "bikesale-uk",
                timeZone = "UTC",
                defaultLanguage = Language.EN,
                defaultTaxRate = BigDecimal("20.00"),
            ),
        )

        workflowDeploymentService.deployWorkflows()
        // Note: Standard workflows are deployed by WorkflowDeploymentService
        // Custom workflow definitions are created through the web portal
    }





    private suspend fun createPaymentConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_BIKESALE_UK)

        createPaymentAccounts(tenantId)
        createPaymentCategorizationRule(tenantId)
    }

    private suspend fun createPaymentAccounts(
        tenantId: UUID,
    ) {
        paymentAccountService.createPaymentAccount(
            PaymentCreator.createPaymentAccount(
                tenantId = tenantId,
                accountId = "*****************",
                name = "JPMorgan Bank Account",
                description = "For Deposit Payments",
                bic = "JPMGB2L",
                accountHolder = "BikeSale Uk Ltd.",
                paymentAccountType = PaymentAccountType.BANK_ACCOUNT,
                defaultAccount = true,
            ),
        )
    }

    private suspend fun createPaymentCategorizationRule(
        tenantId: UUID,
    ) {
        paymentCategorizationRuleService.createPaymentCategorizationRule(
            PaymentCreator.createPaymentCategorizationRule(
                tenantId = tenantId,
                name = "Bank Fees",
                purposes = "* FEE *",
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetPaymentCategory = PaymentCategory.BANK_FEES,
            ),
        )

        paymentCategorizationRuleService.createPaymentCategorizationRule(
            PaymentCreator.createPaymentCategorizationRule(
                tenantId = tenantId,
                name = "Cash Pool",
                purposes = "* TRANSFER *",
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetPaymentCategory = PaymentCategory.CASH_POOLING,
            ),
        )

        paymentCategorizationRuleService.createPaymentCategorizationRule(
            PaymentCreator.createPaymentCategorizationRule(
                tenantId = tenantId,
                name = "Payout",
                purposes = "",
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetPaymentCategory = PaymentCategory.PAYOUT,
            ),
        )

        paymentCategorizationRuleService.createPaymentCategorizationRule(
            PaymentCreator.createPaymentCategorizationRule(
                tenantId = tenantId,
                name = "Customer Payment",
                purposes = "",
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetPaymentCategory = PaymentCategory.CUSTOMER_PAYMENT,
            ),
        )
    }

    private suspend fun createBookingkeepingConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_BIKESALE_UK)

        createChartOfAccounts(tenantId)
        createBookingRules(tenantId)
    }

    private suspend fun createChartOfAccounts(
        tenantId: UUID,
    ) {
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2000,
                name = "JPMorgan Bank Account",
                description = "",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2020,
                name = "UK Corporate Bank",
                description = "",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5060,
                name = "Tacticals",
                description = "",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5210,
                name = "License Plate",
                description = "",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5200,
                name = "Delivery Turnover",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5100,
                name = "Accessories Turnover",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2120,
                name = "Part Exchange",
                description = "",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3300,
                name = "Retailer Fitment",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2011,
                name = "Bank Clearing Account",
                description = "Account for Payment Clearing",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2200,
                name = "Government Grants",
                description = "Accounts receivable towards the government",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3200,
                name = "Output VAT Payable",
                description = "",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3100,
                name = "RFL FRF Payable",
                description = "British registration and road fee to be paid",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5050,
                name = "Discounts",
                description = "Discounts i.e. loyalty bonus, finance",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5010,
                name = "Options Turnover",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5000,
                name = "Base Model Turnover",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2010,
                name = "Market Bank Account",
                description = "Market Bank Account for Deposits arriving from the market",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3000,
                name = "Deposit Holding Account",
                description = "Deposits",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
    }

    private suspend fun createTranslations() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_BIKESALE_UK)

        translationService.createTranslations(
            listOf(
                TranslationCreator.createTranslation(tenantId, GeneralDocumentTranslationKey.COPY.name, Language.EN, "COPY"),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.DEPOSIT_INVOICE.name,
                    Language.EN,
                    "Deposit Invoice",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.FINAL_INVOICE.name,
                    Language.EN,
                    "Final Invoice",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.REVERSED.name,
                    Language.EN,
                    "Credit Note",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.SELF_BILLING.name,
                    Language.EN,
                    "Self Billing",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.SELF_BILLING_REVERSED.name,
                    Language.EN,
                    "Self Billing Reversed",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.COMMISSION.name,
                    Language.EN,
                    "Commission",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.COMMISSION_REVERSED.name,
                    Language.EN,
                    "Commission Reversed",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.DESCRIPTION.name,
                    Language.EN,
                    "Description",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.QTY.name,
                    Language.EN,
                    "Qty",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.UNIT_PRICE.name,
                    Language.EN,
                    "Unit Price",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.TOTAL_PRICE.name,
                    Language.EN,
                    "Total Price",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.SUBTOTAL_AMOUNT.name,
                    Language.EN,
                    "Subtotal (net):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.TAX_AMOUNTS.name,
                    Language.EN,
                    "Tax Amounts (20%):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.TOTAL_AMOUNT.name,
                    Language.EN,
                    "Total Amount:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.INVOICE_NUMBER.name,
                    Language.EN,
                    "Invoice Number:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.INTRO.name,
                    Language.EN,
                    "We allow ourselves to invoice you for the following services:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.INTRO_REVERSED.name,
                    Language.EN,
                    "We allow ourselves to credit you the following services:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.OUTRO.name,
                    Language.EN,
                    "Payable until {{PAYMENT_DUE_DATE}} to our bank account {{BANK_ACCOUNT}}.\n" +
                        "\n" +
                        "Please be sure to specify the invoice number and customer number when making a payment.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.OUTRO_REVERSED.name,
                    Language.EN,
                    "You will receive a refund within 30 days if there is a balance on your account.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.INVOICE_DATE.name,
                    Language.EN,
                    "Invoice Date:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.DELIVERY_DATE.name,
                    Language.EN,
                    "Delivery Date:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.RELATED_DOCUMENT.name,
                    Language.EN,
                    "Original Document:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.RELATED_DOCUMENT_DATE.name,
                    Language.EN,
                    "Original Document Date:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.CUSTOMER_NUMBER.name,
                    Language.EN,
                    "Customer Number:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.CEO.name,
                    Language.EN,
                    "CEO",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.TELEPHONE.name,
                    Language.EN,
                    "Telephone",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.EMAIL.name,
                    Language.EN,
                    "Email",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    BikeSaleUkDocumentToPdfMapping.BikeSaleUkTranslationKey.VAT_ID.name,
                    Language.EN,
                    "VAT ID",
                ),
            ),
        )
    }

    private suspend fun createBookingRules(
        tenantId: UUID,
    ) {
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "BIKE group booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "BIKE",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "POST-TAX ITEMS group booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "POST-TAX ITEMS",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 3100,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Delivery booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "OPTIONS",
                itemArticleNumbers = "RDCHG",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5200,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "License Plates booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "OPTIONS",
                itemArticleNumbers = "LPCHG",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5210,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "General Options booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "OPTIONS",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5010,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "General RetFit booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "RETFIT",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5010,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Discount booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                itemGroups = "DISCOUNT",
                debitAccountNumber = 5050,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 0,
                creditAccountIsCreditor = true,
                creditPostingKey = "01",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Government Grant booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                itemGroups = "GOVERNMENT_GRANT",
                debitAccountNumber = 2200,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 0,
                creditAccountIsCreditor = true,
                creditPostingKey = "01",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Taxes booking",
                bookingRuleType = BookingRuleType.TAX_CODE,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                taxCodes = "*",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 3200,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Incoming payment booking",
                bookingRuleType = BookingRuleType.PAYMENT_TRANSACTION,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                bankAccountNumbers = "*",
                debitAccountNumber = 2010,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Payout booking",
                bookingRuleType = BookingRuleType.PAYMENT_TRANSACTION,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                bankAccountNumbers = "*",
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2010,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Order customer pay-in assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetTypes = PaymentAssignmentTargetType.ORDER.name,
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 3000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Order customer payout assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetTypes = PaymentAssignmentTargetType.ORDER.name,
                debitAccountNumber = 3000,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Final document customer pay-in assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetTypes = PaymentAssignmentTargetType.FINAL.name,
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 3000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Final document customer payout assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetTypes = PaymentAssignmentTargetType.FINAL.name,
                debitAccountNumber = 3000,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Reverse document customer payout assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetTypes = PaymentAssignmentTargetType.REVERSED.name,
                debitAccountNumber = 3000,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Reverse document customer pay-in assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetTypes = PaymentAssignmentTargetType.REVERSED.name,
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 3000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        // self billing
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Selfbill RetFit booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                itemGroups = "RETFIT",
                debitAccountNumber = 5010,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 0,
                creditAccountIsCreditor = true,
                creditPostingKey = "01",
            ),
        )

        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Selfbill booking",
                bookingRuleType = BookingRuleType.TAX_CODE,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                taxCodes = "*",
                debitAccountNumber = 3200,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 0,
                creditAccountIsCreditor = true,
                creditPostingKey = "01",
            ),
        )
    }

    private suspend fun addCustomers() {
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson("John_Doe", "John", "Doe", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson("Sabine_Brown", "Sabine", "Brown", Language.EN),
        )

        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("Unilever_Plc", "Unilever Plc", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("Lidl_UK_Ltd", "Lidl UK Ltd", Language.EN),
        )

        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("Lloyd_Ltd", "Lloyd Ltd", Language.EN),
        )

        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("HSBC_Ltd", "HSBC Ltd", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("Tesco_Ltd", "Tesco Ltd", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("AstraZeneca_Ltd", "AstraZeneca Ltd", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("Diageo_Ltd", "Diageo Ltd", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("BP_Ltd", "BP Ltd", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("Shell_Plc_Ltd", "Shell plc Ltd", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("Ferguson_Ltd", "Ferguson Ltd", Language.EN),
        )
        customerService.createCustomer(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany("British_American_Tobacco_Ltd", "British American Tobacco Ltd", Language.EN),
        )
    }

    suspend fun addSuppliers() {
        supplierService.createSupplier(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            SupplierCreator.create("London_Bike_Dealer_Ltd", "London Bike Dealer Ltd", Language.EN),
        )

        supplierService.createSupplier(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            SupplierCreator.create("South_Hampton_Bike_Dealer_Ltd", "South Hampton Bike Ltd", Language.EN),
        )

        supplierService.createSupplier(
            TENANT_KEY_BIKESALE_UK,
            SYSTEM_USER_KEY,
            SupplierCreator.create("Manchester_Bikes_Ltd", "Manchester Bikes Ltd", Language.EN),
        )
    }

    suspend fun addOrder(
        tenantKey: String,
        customer: Customer,
        businessSegmentKey: String,
        key: String,
        orderSupplierDtos: List<OrderSupplierDto> = emptyList(),
        orderDate: LocalDate,
        currency: Currency,
        financingType: FinancingType,
    ): OrderDomainDto {
        val shippingAddressId = addressLoadService.findLatestCustomerAddress(customer.tenantId, customer.id, AddressType.SHIPPING).addressId
        val shippingAddressKey = addressLoadService.findById(shippingAddressId).key

        val billingAddressId = addressLoadService.findLatestCustomerAddress(customer.tenantId, customer.id, AddressType.BILLING).addressId
        val billingAddressKey: String = addressLoadService.findById(billingAddressId).key

        val createOrderDomainDto = OrderCreator.createOrderDomainDto(
            customerKey = customer.key,
            businessSegmentKey = businessSegmentKey,
            orderKey = key,
            orderDate = orderDate,
            supplierContributions = orderSupplierDtos.map {
                OrderSupplierRequestDomainDto(
                    tenantKey = tenantKey,
                    orderKey = key,
                    supplierKey = it.supplierKey,
                    supplyType = it.supplyType,
                    key = UUID.randomUUID().toString(),
                    commissionRate = it.commissionRate,
                    commissionAmount = it.commissionAmount,
                    commissionCurrency = it.commissionCurrency,
                    taxInformationDomainDto = it.taxInformation,
                    properties = it.properties,
                )
            },
            financingType = financingType,
            billingAddressKey = billingAddressKey,
            shippingAddressKey = shippingAddressKey,
            currency = currency,
            properties = mutableMapOf(PropertyKey.VIN to DomainUtil.randomVIN(), PropertyKey.MODEL_CODE to "BIKE_4312"),
        )

        val items = mutableListOf<OrderItemRequestDomainDto>()

        items.add(createBikeOrderItem(createOrderDomainDto, currency))
        items.add(createColorBlackItem(createOrderDomainDto, currency))
        items.add(createWheelsItem(createOrderDomainDto, currency))

        for (i in 0..30) {
            items.add(createRandomOptionItem(createOrderDomainDto, currency))
        }

        val selfBillingKeys = orderSupplierDtos.filter { it.supplyType == SupplyType.SELFBILLING }.map { it.supplierKey }
        for (i in selfBillingKeys.indices) {
            items.add(createRetFitOrderItem(createOrderDomainDto, i, currency, selfBillingKeys))
        }

        items.add(createDiscountOrderItem(createOrderDomainDto, 0, currency))

        items.add(createGovernmentGrantOrderItem(createOrderDomainDto, 0, currency))

        workflowApiService.startOrderWorkflow(tenantKey, SYSTEM_USER_KEY, createOrderDomainDto.copy(items = items))

        return createOrderDomainDto
    }

    private fun createRetFitOrderItem(
        createOrderDomainDto: OrderDomainDto,
        i: Int,
        currency: Currency,
        supplierKeys: List<String>,
    ): OrderItemRequestDomainDto = if (i % 2 == 0) {
        createBicycleHolderRetFit(createOrderDomainDto, i, currency, supplierKeys[i])
    } else {
        createRoofBoxRetFit(createOrderDomainDto, i, currency, supplierKeys[i])
    }

    private fun createBicycleHolderRetFit(
        createOrderDomainDto: OrderDomainDto,
        i: Int,
        currency: Currency,
        supplierKey: String,
    ): OrderItemRequestDomainDto {
        val orderItem = OrderCreator.createDebitOrderItem(
            itemGroup = "RETFIT",
            key = createOrderDomainDto.key + "_Retfit_" + i,
            name = "Bicycle Holder",
            articleNumber = "RETBH1",
            unitNetPrice = BigDecimal("250.50"),
            currency = currency,
            supplierKey = supplierKey,
            taxRate = BigDecimal("20.0"),
            taxCode = TaxCode.A1,
        )
        return orderItem
    }

    private fun createRoofBoxRetFit(
        createOrderDomainDto: OrderDomainDto,
        i: Int,
        currency: Currency,
        supplierKey: String,
    ): OrderItemRequestDomainDto {
        val orderItem = OrderCreator.createDebitOrderItem(
            itemGroup = "RETFIT",
            key = createOrderDomainDto.key + "_Retfit_" + i,
            name = "Roof Box",
            articleNumber = "RETRB2",
            unitNetPrice = BigDecimal("990.50"),
            currency = currency,
            supplierKey = supplierKey,
            taxRate = BigDecimal("20.0"),
            taxCode = TaxCode.A1,
        )
        return orderItem
    }

    private fun createBikeOrderItem(
        createOrderDomainDto: OrderDomainDto,
        currency: Currency,
    ): OrderItemRequestDomainDto = when (createOrderDomainDto.businessSegmentKey) {
        CUBE_CASH, CUBE_FINANCED -> createCubeBikeItem(createOrderDomainDto, currency)
        CANYON_CASH, CANYON_FINANCED -> createCanyonBikeItem(createOrderDomainDto, currency)
        else -> throw IllegalArgumentException("Unknown business segment: ${createOrderDomainDto.businessSegmentKey}")
    }

    private fun createCubeBikeItem(
        createOrderDomainDto: OrderDomainDto,
        currency: Currency,
    ): OrderItemRequestDomainDto {
        val vehicleItem =
            OrderCreator.createDebitOrderItem(
                itemGroup = "BIKE",
                key = createOrderDomainDto.key + "Bike",
                name = "Cube Stereo Hybrid 120 Pro 750 Allroad",
                articleNumber = "CU120",
                unitNetPrice = BigDecimal("13000.50"),
                currency = currency,
                taxRate = BigDecimal("20.0"),
                taxCode = TaxCode.A1,
            )
        vehicleItem.properties[PropertyKey.MODEL_CODE] = "BIKE_CUBE_STEREO"
        return vehicleItem
    }

    private fun createCanyonBikeItem(
        createOrderDomainDto: OrderDomainDto,
        currency: Currency,
    ): OrderItemRequestDomainDto {
        val vehicleItem =
            OrderCreator.createDebitOrderItem(
                itemGroup = "BIKE",
                key = createOrderDomainDto.key + "Bike",
                name = "Canyon Neuron:ONfly CF 9",
                articleNumber = "CANNOF",
                unitNetPrice = BigDecimal("4999.30"),
                currency = currency,
                taxRate = BigDecimal("20.0"),
                taxCode = TaxCode.A1,
            )
        vehicleItem.properties[PropertyKey.MODEL_CODE] = "BIKE_CANYON_NEURON"
        return vehicleItem
    }

    private fun createColorBlackItem(
        createOrderDomainDto: OrderDomainDto,
        currency: Currency,
    ) = OrderCreator.createDebitOrderItem(
        itemGroup = "OPTIONS",
        key = "${createOrderDomainDto.key}_color_black_",
        name = "Color 'Black'",
        articleNumber = "B4B4",
        unitNetPrice = BigDecimal("320.22"),
        currency = currency,
        taxRate = BigDecimal("20.0"),
        taxCode = TaxCode.A1,
    )

    private fun createRandomOptionItem(
        createOrderDomainDto: OrderDomainDto,
        currency: Currency,
    ) = OrderCreator.createDebitOrderItem(
        itemGroup = "OPTIONS",
        key = "${createOrderDomainDto.key}_randomoption_${UUID.randomUUID()}",
        name = "Bike Lock - Abus Granit Super Extreme",
        articleNumber = "BLABUS",
        unitNetPrice = BigDecimal("300.10"),
        currency = currency,
        taxRate = BigDecimal("20.0"),
        taxCode = TaxCode.A1,
    )

    private fun createWheelsItem(
        createOrderDomainDto: OrderDomainDto,
        currency: Currency,
    ) = OrderCreator.createDebitOrderItem(
        itemGroup = "OPTIONS",
        key = "${createOrderDomainDto.key}_bike_lock_abus",
        name = "Bike Lock - Abus Granit Super Extreme",
        articleNumber = "BLABUS",
        unitNetPrice = BigDecimal("300.10"),
        currency = currency,
        taxRate = BigDecimal("20.0"),
        taxCode = TaxCode.A1,
    )

    private fun createDiscountOrderItem(
        createOrderDomainDto: OrderDomainDto,
        i: Int,
        currency: Currency,
    ): OrderItemRequestDomainDto {
        val orderItem =
            OrderCreator.createCreditOrderItem(
                itemGroup = "DISCOUNT",
                key = createOrderDomainDto.key + "_Discount_" + i,
                name = "Loyalty Bonus",
                articleNumber = "DISLOY",
                unitPrice = BigDecimal("250.35"),
                currency = currency,
                taxRate = BigDecimal("20.0"),
                taxCode = TaxCode.A1,
            )
        return orderItem
    }

    private fun createGovernmentGrantOrderItem(
        createOrderDomainDto: OrderDomainDto,
        i: Int,
        currency: Currency,
    ): OrderItemRequestDomainDto {
        val orderItem =
            OrderCreator.createCreditOrderItem(
                itemGroup = "GOVERNMENT_GRANT",
                key = createOrderDomainDto.key + "_GovernmentGrant_" + i,
                name = "Government Grant",
                articleNumber = "GR1",
                unitPrice = BigDecimal("500.00"),
                currency = currency,
                taxRate = BigDecimal("0.00"),
                taxCode = TaxCode.A9,
            )
        return orderItem
    }

    private suspend fun addOrders() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_BIKESALE_UK)
        val johnDoe = customerLoadService.findByTenantIdAndKey(tenantId, "John_Doe")

        val now = TimeProvider.nowDateInTimeZone(tenantLoadService.findTenantConfigByTenantId(tenantId).getTimeZoneAsObject())

        val currency = Currency.GBP

        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = johnDoe,
            businessSegmentKey = CUBE_CASH,
            key = "JD_1",
            orderSupplierDtos = listOf(
                OrderSupplierDto(
                    supplierKey = "London_Bike_Dealer_Ltd",
                    supplyType = SupplyType.SELFBILLING,
                    properties = mutableMapOf(PropertyKey.REMARKS to "Selfbilling to JD_1"),
                ),
            ),
            orderDate = now.minusDays(6),
            currency = currency,
            financingType = FinancingType.CASH,
        )

        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = johnDoe,
            businessSegmentKey = CUBE_FINANCED,
            key = "JD_2",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(6),
            currency = currency,
            financingType = FinancingType.LEASING,
        )

        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = johnDoe,
            businessSegmentKey = CUBE_FINANCED,
            key = "JD_3",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(5),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )

        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = johnDoe,
            businessSegmentKey = CUBE_FINANCED,
            key = "JD_4",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(3),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = johnDoe,
            businessSegmentKey = CUBE_FINANCED,
            key = "JD_5",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(1),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = johnDoe,
            businessSegmentKey = CUBE_FINANCED,
            key = "JD_6",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(1),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = johnDoe,
            businessSegmentKey = CUBE_FINANCED,
            key = "JD_7",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now,
            currency = currency,
            financingType = FinancingType.FINANCED,
        )

        val sabineBrown = customerLoadService.findByTenantIdAndKey(tenantId, "Sabine_Brown")
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = sabineBrown,
            businessSegmentKey = CANYON_CASH,
            key = "SB_1",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(3),
            currency = currency,
            financingType = FinancingType.CASH,
        )

        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = sabineBrown,
            businessSegmentKey = CANYON_FINANCED,
            key = "SB_2",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(3),
            currency = currency,
            financingType = FinancingType.LEASING,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = sabineBrown,
            businessSegmentKey = CANYON_FINANCED,
            key = "SB_3",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(2),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = sabineBrown,
            businessSegmentKey = CANYON_FINANCED,
            key = "SB_4",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(3),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = sabineBrown,
            businessSegmentKey = CANYON_FINANCED,
            key = "SB_5",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(1),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = sabineBrown,
            businessSegmentKey = CANYON_FINANCED,
            key = "SB_6",
            orderSupplierDtos = listOf(
                OrderSupplierDto(supplierKey = "London_Bike_Dealer_Ltd", supplyType = SupplyType.SELFBILLING),
            ),
            orderDate = now.minusDays(6),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = sabineBrown,
            businessSegmentKey = CANYON_FINANCED,
            key = "SB_7",
            orderSupplierDtos = listOf(
                OrderSupplierDto(
                    supplierKey = "London_Bike_Dealer_Ltd",
                    supplyType = SupplyType.COMMISSION,
                    commissionAmount = BigDecimal("12000"),
                    commissionCurrency = currency,
                    taxInformation = TaxInformationDomainDto(
                        taxRate = BigDecimal("20.00"),
                        taxCode = TaxCode.A1,
                        currency = currency,
                    ),
                    properties = mutableMapOf(
                        PropertyKey.REMARKS to "Commission for order SB_7",
                    ),
                ),
            ),
            orderDate = now,
            currency = currency,
            financingType = FinancingType.FINANCED,
        )

        val unileverPlc = customerLoadService.findByTenantIdAndKey(tenantId, "Unilever_Plc")
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = unileverPlc,
            businessSegmentKey = CUBE_CASH,
            key = "UP_1",
            orderSupplierDtos = listOf(
                OrderSupplierDto(
                    supplierKey = "London_Bike_Dealer_Ltd",
                    supplyType = SupplyType.COMMISSION,
                    commissionRate = BigDecimal("12.00"),
                    commissionCurrency = currency,
                    taxInformation = TaxInformationDomainDto(
                        taxRate = BigDecimal("20.00"),
                        taxCode = TaxCode.A1,
                        currency = currency,
                    ),
                    properties = mutableMapOf(
                        PropertyKey.REMARKS to "Commission for order UP_1",
                    ),
                ),
            ),
            orderDate = now.minusDays(2),
            currency = currency,
            financingType = FinancingType.CASH,
        )

        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = unileverPlc,
            businessSegmentKey = CANYON_FINANCED,
            key = "UP_2",
            orderDate = now.minusDays(4),
            currency = currency,
            financingType = FinancingType.LEASING,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = unileverPlc,
            businessSegmentKey = CANYON_FINANCED,
            key = "UP_3",
            orderDate = now.minusDays(6),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = unileverPlc,
            businessSegmentKey = CANYON_FINANCED,
            key = "UP_4",
            orderDate = now.minusDays(1),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = unileverPlc,
            businessSegmentKey = CANYON_FINANCED,
            key = "UP_5",
            orderDate = now.minusDays(1),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = unileverPlc,
            businessSegmentKey = CANYON_FINANCED,
            key = "UP_6",
            orderDate = now.minusDays(2),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
        addOrder(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            customer = unileverPlc,
            businessSegmentKey = CANYON_FINANCED,
            key = "UP_7",
            orderDate = now.minusDays(2),
            currency = currency,
            financingType = FinancingType.FINANCED,
        )
    }

    private suspend fun addNotes() {
        addNote(userKey = "<EMAIL>", orderKey = "JD_1", content = "This is a note for order JD_1")
        addNote(userKey = "<EMAIL>", orderKey = "JD_1", content = "My second note for order JD_1")
        addNote(userKey = "<EMAIL>", orderKey = "JD_1", content = "My third note for order JD_1")
        addNote(userKey = "<EMAIL>", orderKey = "JD_1", content = "My fourth note for order JD_1")
        addNote(userKey = "<EMAIL>", orderKey = "JD_1", content = "My fifth note for order JD_1")

        addNote(userKey = "<EMAIL>", orderKey = "JD_3", content = "This is a note for order JD_3")
    }

    private suspend fun addNote(
        userKey: String,
        orderKey: String,
        content: String,
    ) {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_BIKESALE_UK)
        val order = orderLoadService.findByTenantIdAndKey(tenantId, orderKey)

        val user = userLoadService.findByKey(userKey)

        noteService.createNote(
            NoteCreator.createNote(
                tenantId = tenantId,
                createdBy = user.id,
                targetType = TargetType.ORDER,
                targetKey = order.key,
                targetId = order.id,
                content = content,
            ),
        )
    }

    private suspend fun addPaymentTransactions() {
        val bikeSaleUkId = tenantLoadService.fetchTenantId(TENANT_KEY_BIKESALE_UK)
        paymentTransactionService.createPaymentTransaction(
            createPaymentTransaction(
                bikeSaleUkId,
                DebitCreditIndicator.CREDIT,
                DomainUtil.randomKey(),
                "22244567",
                LocalDate.of(2024, 6, 28),
                Money.of(BigDecimal("5050.50"), Currency.GBP),
                "Payment for Order 1",
                paymentCategory = PaymentCategory.CUSTOMER_PAYMENT,
            ),
        )

        paymentTransactionService.createPaymentTransaction(
            createPaymentTransaction(
                bikeSaleUkId,
                DebitCreditIndicator.CREDIT,
                DomainUtil.randomKey(),
                "********",
                LocalDate.of(2024, 7, 15),
                Money.of(BigDecimal("1000000.00"), Currency.GBP),
                "Bulk Payment",
                paymentCategory = PaymentCategory.CUSTOMER_PAYMENT,
            ),
        )

        paymentTransactionService.createPaymentTransaction(
            createPaymentTransaction(
                bikeSaleUkId,
                DebitCreditIndicator.DEBIT,
                DomainUtil.randomKey(),
                "********",
                LocalDate.of(2024, 7, 2),
                Money.of(BigDecimal("50.50"), Currency.GBP),
                "Payment for Order 1",
                paymentCategory = PaymentCategory.PAYOUT,
            ),
        )

        val order = orderLoadService.findByTenantIdAndKey(bikeSaleUkId, "JD_3")

        paymentTransactionService.createPaymentTransaction(
            createPaymentTransaction(
                tenantId = bikeSaleUkId,
                assignmentStatus = PaymentTransactionAssignmentStatus.NEW,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                accountSettlementItemId = DomainUtil.randomKey(),
                accountId = "********",
                bookingDate = LocalDate.of(2024, 6, 24),
                totalAmount = Money.of(order.totalGrossAmount, order.currency),
                text = order.key,
                paymentCategory = PaymentCategory.CUSTOMER_PAYMENT,
            ),
        )

        paymentTransactionService.createPaymentTransaction(
            createPaymentTransaction(
                bikeSaleUkId,
                DebitCreditIndicator.CREDIT,
                DomainUtil.randomKey(),
                "********",
                LocalDate.of(2024, 7, 15),
                Money.of(BigDecimal("1000000.00"), Currency.GBP),
                "Bulk Payment",
                paymentCategory = PaymentCategory.CUSTOMER_PAYMENT,
            ),
        )
    }

    private suspend fun addPaymentAssignments(
        paymentTransaction: PaymentTransaction,
        orderKeys: List<String>,
    ) {
        for (orderKey in orderKeys) {
            val order = orderLoadService.findByTenantIdAndKey(paymentTransaction.tenantId, orderKey)

            val paymentAssignment = PaymentAssignmentDomainDto(
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                paymentTransactionKey = paymentTransaction.key,
                assignedEntityKey = order.key,
                targetType = PaymentAssignmentTargetType.ORDER,
                origin = PaymentAssignmentOrigin.AUTOMATED,
                assignmentDateTime = TimeProvider.nowDateTimeInUTC(),
                type = PaymentAssignmentType.ASSIGNED,
                amount = order.totalGrossAmount,
                currency = order.currency,
                key = DomainUtil.randomKey(),
                status = PaymentAssignmentStatus.ACTIVE,
            )

            orderPaymentTransactionService.assignPayment(
                tenantId = order.tenantId,
                userId = SYSTEM_USER_ID,
                order = order,
                paymentAssignmentDomainDto = paymentAssignment,
            )
        }
    }

    private suspend fun createPaymentTransaction(
        tenantId: UUID,
        debitCreditIndicator: DebitCreditIndicator,
        accountSettlementItemId: String,
        accountId: String,
        bookingDate: LocalDate,
        totalAmount: Money,
        text: String,
        assignmentStatus: PaymentTransactionAssignmentStatus = PaymentTransactionAssignmentStatus.UNASSIGNED,
        paymentCategory: PaymentCategory,
    ): PaymentTransaction {
        val paymentTransaction =
            PaymentTransaction(
                assignmentStatus = assignmentStatus,
                debitCreditIndicator = debitCreditIndicator,
                settlementReportItemId = accountSettlementItemId,
                accountNumber = accountId,
                bookingDate = bookingDate,
                totalAmount = totalAmount.amount,
                currency = totalAmount.currency,
                text = text,
                createdBy = SYSTEM_USER_ID,
                tenantId = tenantId,
                key = uniqueIdGeneratorService.generatePaymentTransactionNumber(tenantId).toString(),
                lastModifiedBy = SYSTEM_USER_ID,
                paymentCategory = paymentCategory,
            )
        return paymentTransaction
    }

    private suspend fun createCucumberNotificationApiReceiver() {
        notificationApiReceiverService.createNotificationApiReceiver(
            tenantKey = TENANT_KEY_BIKESALE_UK,
            userKey = SYSTEM_USER_KEY,
            notificationApiReceiverDomainDto = NotificationReceiverCreator.createCucumberNotificationReceiver(),
        )
    }
}

data class OrderSupplierDto(
    val supplierKey: String,
    val supplyType: SupplyType,
    val commissionAmount: BigDecimal? = null,
    val commissionRate: BigDecimal? = null,
    val commissionCurrency: Currency? = null,
    val taxInformation: TaxInformationDomainDto? = null,
    val properties: MutableMap<PropertyKey, String> = EnumMap(PropertyKey::class.java),
)
