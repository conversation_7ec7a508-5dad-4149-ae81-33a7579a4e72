package com.klosesoft.billingsolution.databasefiller.creator

import com.klosesoft.billingsolution.common.utils.authtypes.NoAuthorization
import com.klosesoft.billingsolution.domain.model.dto.NotificationApiReceiverDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.NotificationApiSubpathConfig
import com.klosesoft.billingsolution.domain.model.valueobject.NotificationType

object NotificationReceiverCreator {

    fun createCucumberNotificationReceiver() = NotificationApiReceiverDomainDto(
        key = "CUCUMBER",
        baseUrl = "http://localhost:12000",
        authorization = NoAuthorization(),
        configs = listOf(
            NotificationApiSubpathConfig(
                subpath = "/v1/notifications",
                notificationTypes = NotificationType.entries,
            ),
        ),
        version = 0,
    )
}
