package com.klosesoft.billingsolution.databasefiller.tenant

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_ID
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_KEY
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_MVZ_GESUND
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.databasefiller.creator.BookkeepingCreator
import com.klosesoft.billingsolution.databasefiller.creator.CustomerCreator
import com.klosesoft.billingsolution.databasefiller.creator.NotificationReceiverCreator
import com.klosesoft.billingsolution.databasefiller.creator.OrderCreator
import com.klosesoft.billingsolution.databasefiller.creator.PaymentCreator
import com.klosesoft.billingsolution.databasefiller.creator.ReportConfigCreator
import com.klosesoft.billingsolution.databasefiller.creator.TenantCreator
import com.klosesoft.billingsolution.databasefiller.creator.TranslationCreator
import com.klosesoft.billingsolution.domain.logic.api.job.report.ReportJob
import com.klosesoft.billingsolution.domain.logic.api.service.WorkflowApiService
import com.klosesoft.billingsolution.domain.logic.service.OrderTemplateItemService
import com.klosesoft.billingsolution.domain.logic.service.OrderTemplateService
import com.klosesoft.billingsolution.domain.logic.service.RoundingUtil
import com.klosesoft.billingsolution.domain.logic.service.billing.PredefinedItemService
import com.klosesoft.billingsolution.domain.logic.service.billing.TranslationService
import com.klosesoft.billingsolution.domain.logic.service.billing.mapping.MvzGesundDocumentToPdfMapping
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingAccountService
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingRuleService
import com.klosesoft.billingsolution.domain.logic.service.common.AddressService
import com.klosesoft.billingsolution.domain.logic.service.common.BusinessSegmentService
import com.klosesoft.billingsolution.domain.logic.service.common.CustomerService
import com.klosesoft.billingsolution.domain.logic.service.common.NotificationApiReceiverService
import com.klosesoft.billingsolution.domain.logic.service.common.TenantService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentAccountService
import com.klosesoft.billingsolution.domain.logic.service.user.RoleService
import com.klosesoft.billingsolution.domain.logic.service.user.UserService
import com.klosesoft.billingsolution.domain.model.dto.AddressDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderItemRequestDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.AddressType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingRuleType
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.EmailTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.domain.model.valueobject.FinancingType
import com.klosesoft.billingsolution.domain.model.valueobject.GeneralDocumentTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.ItemCategory
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentTargetType
import com.klosesoft.billingsolution.domain.model.valueobject.PropertyKey
import com.klosesoft.billingsolution.domain.model.valueobject.ReportFormat
import com.klosesoft.billingsolution.domain.model.valueobject.ReportInterval
import com.klosesoft.billingsolution.domain.model.valueobject.ReportType
import com.klosesoft.billingsolution.domain.model.valueobject.Right
import com.klosesoft.billingsolution.domain.model.valueobject.TargetType
import com.klosesoft.billingsolution.domain.model.valueobject.TaxCode
import com.klosesoft.billingsolution.domain.model.valueobject.TaxInformation
import com.klosesoft.billingsolution.domain.model.valueobject.TaxType
import com.klosesoft.billingsolution.domain.model.valueobject.TranslationType
import com.klosesoft.billingsolution.persistence.model.entity.Customer
import com.klosesoft.billingsolution.persistence.model.entity.OrderTemplate
import com.klosesoft.billingsolution.persistence.model.entity.PredefinedItem
import com.klosesoft.billingsolution.persistence.model.entity.Role
import com.klosesoft.billingsolution.persistence.service.AddressLoadService
import com.klosesoft.billingsolution.persistence.service.BusinessSegmentLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.RoleLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import com.klosesoft.billingsolution.workflow.WorkflowDeploymentService
import org.springframework.core.env.Environment
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Component
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Locale
import java.util.UUID

@Component
class MvzGesundInitializer(
    private val tenantService: TenantService,
    private val tenantLoadService: TenantLoadService,
    private val customerLoadService: CustomerLoadService,
    private val businessSegmentService: BusinessSegmentService,
    private val userService: UserService,
    private val userLoadService: UserLoadService,
    private val customerService: CustomerService,
    private val workflowApiService: WorkflowApiService,
    private val addressLoadService: AddressLoadService,
    private val notificationApiReceiverService: NotificationApiReceiverService,
    private val bookingAccountService: BookingAccountService,
    private val bookingRuleService: BookingRuleService,
    private val environment: Environment,
    private val roleLoadService: RoleLoadService,
    private val roleService: RoleService,
    private val paymentAccountService: PaymentAccountService,
    private val reportConfigCreator: ReportConfigCreator,
    private val reportJob: ReportJob,
    private val translationService: TranslationService,
    private val resourceLoader: ResourceLoader,
    private val addressService: AddressService,
    private val workflowDeploymentService: WorkflowDeploymentService,
    private val predefinedItemService: PredefinedItemService,
    private val orderTemplateService: OrderTemplateService,
    private val orderTemplateItemService: OrderTemplateItemService,
    private val businessSegmentLoadService: BusinessSegmentLoadService,
) {

    suspend fun setupTenant() {
        createTenant()
        createBusinessSegments()
        createTenantWebPortalUsers()
        createBookingkeepingConfig()
        createPaymentConfig()
        createTranslations()
        createPredefinedItems()

        if (environment.activeProfiles.contains("demo")) {
            addCustomers()
            addOrders()
            addOrderTemplates()
            createReports()
        }

        if (environment.activeProfiles.contains("cucumber")) {
            createCucumberNotificationApiReceiver()
        }
    }

    private suspend fun addOrderTemplates() {
        addInterneKardioUntersuchungTemplate()

        addVerschlussGaumenTemplate()

        addHypsensibilisierungTemplate()
    }

    private suspend fun addInterneKardioUntersuchungTemplate() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND)

        val orderTemplate = addOrderTemplate(tenantId, "INTERNE_KARDIO_UNTERSUCHERUNG", "Interne kardiotokographische Untersuchung")

        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "1",
            key = orderTemplate.key + "_3",
            title = "Beratung – auch mittels Fernsprecher",
            unitNetAmount = BigDecimal("4.66"),
            currency = orderTemplate.currency,
            quantity = BigDecimal("2.3"),
        )

        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "250",
            key = orderTemplate.key + "_1",
            title = "Blutentnahme mittels Spritze, Kanüle oder Katheter aus der Vene",
            unitNetAmount = BigDecimal("2.33"),
            currency = orderTemplate.currency,
        )

        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "1002",
            key = orderTemplate.key + "_2",
            title = "Externe kardiotokographische Untersuchung",
            unitNetAmount = BigDecimal("11.66"),
            currency = orderTemplate.currency,
            quantity = BigDecimal("2.3"),
        )

        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "1003",
            key = orderTemplate.key + "_0",
            title = "Interne kardiotokographische Untersuchung – gegebenenfalls einschließlich einer",
            unitNetAmount = BigDecimal("22.09"),
            currency = orderTemplate.currency,
        )
    }

    private suspend fun addVerschlussGaumenTemplate() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND)

        val orderTemplate = addOrderTemplate(tenantId, "VERSCHLUSS_GAUMEN", "Verschluss des weichen oder harten Gaumens")

        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "1",
            key = orderTemplate.key + "_3",
            title = "Beratung – auch mittels Fernsprecher",
            unitNetAmount = BigDecimal("4.66"),
            currency = orderTemplate.currency,
            quantity = BigDecimal("2.3"),
        )

        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "2625",
            key = orderTemplate.key + "_1",
            title = "Verschluss des weichen oder harten Gaumens oder Verschluss von perforierenden De",
            unitNetAmount = BigDecimal("72.86"),
            currency = orderTemplate.currency,
            quantity = BigDecimal("2.3"),
        )

        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "4006",
            key = orderTemplate.key + "_2",
            title = "Höchstwert für die Leistung nach Nummer 4006",
            unitNetAmount = BigDecimal("209.83"),
            currency = orderTemplate.currency,
            quantity = BigDecimal("2.3"),
        )
    }

    private suspend fun addHypsensibilisierungTemplate() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND)

        val orderTemplate = addOrderTemplate(tenantId, "HYPOSENSIBILISIERUNG", "Hyposensibilisierung")
        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "1",
            key = orderTemplate.key + "_1",
            title = "Beratung – auch mittels Fernsprecher",
            unitNetAmount = BigDecimal("4.66"),
            currency = orderTemplate.currency,
            quantity = BigDecimal("2.3"),
        )
        addOrderTemplateItem(
            tenantId = tenantId,
            orderTemplate = orderTemplate,
            articleNumber = "263",
            key = orderTemplate.key + "_2",
            title = "Subkutane Hyposensibilisierungsbehandlung (Desensibilisierung), je Sitzung",
            unitNetAmount = BigDecimal("5.25"),
            currency = orderTemplate.currency,
            quantity = BigDecimal("2.3"),
        )
    }

    private suspend fun addOrderTemplate(
        tenantId: UUID,
        title: String,
        description: String,
    ): OrderTemplate {
        val businessSegment = businessSegmentLoadService.findBusinessSegment(tenantId, "PRIVAT")

        val currency = Currency.EUR
        val orderTemplate = orderTemplateService.createOrderTemplate(
            OrderCreator.createOrderTemplate(
                tenantId = tenantId,
                businessSegmentId = businessSegment.id,
                key = title,
                title = title,
                description = description,
                currency = currency,
            ),
        )
        return orderTemplate
    }

    private suspend fun addOrderTemplateItem(
        tenantId: UUID,
        orderTemplate: OrderTemplate,
        articleNumber: String,
        key: String,
        title: String,
        unitNetAmount: BigDecimal,
        currency: Currency,
        taxRate: BigDecimal = BigDecimal.ZERO,
        quantity: BigDecimal = BigDecimal.ONE,
    ) {
        orderTemplateItemService.createOrderTemplateItem(
            OrderCreator.createOrderTemplateItem(
                tenantId = tenantId,
                orderTemplateId = orderTemplate.id,
                key = key,
                articleNumber = articleNumber,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                category = ItemCategory.SERVICE,
                name = title,
                description = null,
                unitNetAmount = unitNetAmount,
                currency = currency,
                itemGroup = "GENERAL",
                quantity = quantity,
                taxes = listOf(
                    TaxInformation(
                        TaxType.VAT,
                        TaxCode.D7,
                        taxRate,
                        RoundingUtil.round(taxRate.multiply(unitNetAmount).multiply(quantity), currency),
                        currency,
                    ),
                ),
            ),
        )
    }

    data class GoaItem(val number: String, val description: String, val price: String)

    private suspend fun createPredefinedItems() {
        val resource = resourceLoader.getResource("classpath:/goa.json")

        // Step 2: Parse JSON content
        val mapper = jacksonObjectMapper()
        val goaItems: List<GoaItem> =
            mapper.readValue(resource.contentAsByteArray, mapper.typeFactory.constructCollectionType(List::class.java, GoaItem::class.java))

        // Step 3: Process the data
        goaItems.forEach { item ->
            predefinedItemService.insertPredefinedItem(
                PredefinedItem(
                    articleNumber = item.number,
                    description = "",
                    quantity = BigDecimal.valueOf(2.3),
                    unitNetAmount = item.price.toBigDecimal(),
                    tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND),
                    debitCreditIndicator = DebitCreditIndicator.DEBIT,
                    taxes = listOf(
                        TaxInformation(
                            TaxType.VAT,
                            TaxCode.D7,
                            BigDecimal.ZERO,
                            BigDecimal.ZERO,
                            Currency.EUR,
                        ),
                    ),
                    category = ItemCategory.SERVICE,
                    name = item.description,
                    currency = Currency.EUR,
                    key = "goa_${item.number}",
                    createdBy = SYSTEM_USER_ID,
                    lastModifiedBy = SYSTEM_USER_ID,
                ),
            )
        }
    }

    private suspend fun createReports() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_MVZ_GESUND).id
        reportConfigCreator.createConfig(tenantId, ReportType.PAYMENT, ReportFormat.CSV, ReportInterval.DAILY, "CET")
        reportConfigCreator.createConfig(tenantId, ReportType.SUBLEDGER, ReportFormat.CSV, ReportInterval.DAILY, "CET")

        reportJob.generateSubledgerReport()
        reportJob.generatePaymentReport()
    }

    private suspend fun createTenantWebPortalUsers() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_MVZ_GESUND).id

        createRoles(tenantId)
        createUserRoles(tenantId)
        createRoleRights(tenantId)
    }

    private suspend fun createRoles(
        tenantId: UUID,
    ) {
        roleService.createRole(createRole("Read-only", tenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Full Access", tenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Administration", tenantId), emptyList(), SYSTEM_USER_ID)
    }

    private suspend fun createRole(
        name: String,
        tenantId: UUID,
    ) = Role(
        name = name,
        createdBy = SYSTEM_USER_ID,
        lastModifiedBy = SYSTEM_USER_ID,
        key = name,
        tenantId = tenantId,
    )

    private suspend fun createUserRoles(
        tenantId: UUID,
    ) {
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)

        setRolesForUser("<EMAIL>", listOf("Read-only"), tenantId)
    }

    private suspend fun setRolesForUser(
        key: String,
        roleKeys: List<String>,
        tenant: UUID,
    ) {
        val user = userLoadService.findByKey(key)

        val roles = mutableListOf<Role>()

        roleKeys.forEach { roleKey ->
            val role = roleLoadService.findByTenantIdAndKey(tenant, roleKey)
            roles.add(role)
        }

        userService.updateUser(user, roles, SYSTEM_USER_ID)
    }

    private suspend fun createRoleRights(
        tenantId: UUID,
    ) {
        addReadOnlyRights(tenantId)
        addFullAccessRights(tenantId)
        addAdminRights(tenantId)
    }

    private suspend fun addReadOnlyRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Read-only")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addFullAccessRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Full Access")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ || it.category == Right.Category.WRITE
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addAdminRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Administration")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.ADMIN || it.category == Right.Category.INTERNAL
            },
            SYSTEM_USER_ID,
        )
    }

    private fun getLogo(): ByteArray {
        val logoResource = resourceLoader.getResource("classpath:/logos/logo-mvz-gesund.png")

        if (logoResource.exists()) {
            try {
                return logoResource.contentAsByteArray
            } catch (e: IOException) {
                throw IllegalStateException("error while reading logo", e)
            }
        } else {
            throw IllegalStateException("logo was not found")
        }
    }

    private suspend fun createBusinessSegments() {
        val logo = getLogo()

        val originatorAddress = addressService.createAddress(
            TENANT_KEY_MVZ_GESUND,
            SYSTEM_USER_KEY,
            AddressDomainDto(
                UUID.randomUUID().toString(),
                companyName = "MVZ Gesund GmbH",
                street = "Herzstraße",
                houseNumber = "1",
                city = "Berlin",
                country = "DE",
                postalCode = "10115",
                mailAddress = "",
            ),
        )

        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_MVZ_GESUND).id
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantId,
                "PRIVAT",
                "'PRIV-'&\$year&'-'&\$numberRange(100000,500000)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "DE783451789",
            ),
        )
    }

    protected suspend fun createTenant() {
        val tenant = tenantService.createTenant(TenantCreator.createActiveTenant(TENANT_KEY_MVZ_GESUND))
        tenantService.createTenantConfig(
            TenantCreator.createTenantConfig(
                tenantId = tenant.id,
                appClientId = "4c1dvp58l0a7d8lbqvckq2pc59",
                orderWorkflow = BillingSolutionConstants.MVZ_GESUND_ORDER_WORKFLOW,
                ledgerCurrency = Currency.EUR,
                features = listOf(Feature.ENABLE_BALANCE_CASE),
                locale = Locale.of("de", "DE"),
                theme = "mvz-gesund",
                timeZone = "CET",
                defaultLanguage = Language.DE,
                defaultTaxRate = BigDecimal.ZERO,
            ),
        )

        workflowDeploymentService.deployWorkflows()
        // Note: Standard workflows are deployed by WorkflowDeploymentService
        // Custom workflow definitions are created through the web portal
    }



    private suspend fun addCustomers() {
        customerService.createCustomer(
            TENANT_KEY_MVZ_GESUND,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                "1000098",
                "Peter",
                "Schmidt",
                Language.DE,
                city = "Ulm",
                country = "DE",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_MVZ_GESUND,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                "1000189",
                "Lisa",
                "Meier",
                Language.DE,
                city = "Hamburg",
                country = "DE",
            ),
        )
    }

    suspend fun addOrder(
        tenantKey: String,
        customer: Customer,
        businessSegmentKey: String,
        key: String,
        orderDate: LocalDate,
        currency: Currency,
        items: List<OrderItemRequestDomainDto>,
        diagnose: String,
    ) {
        val shippingAddressId =
            addressLoadService.findLatestCustomerAddress(customer.tenantId, customer.id, AddressType.SHIPPING).addressId

        val billingAddressId =
            addressLoadService.findLatestCustomerAddress(customer.tenantId, customer.id, AddressType.BILLING)
                .addressId

        val billingAddressKey: String = addressLoadService.findById(billingAddressId).key

        val shippingAddressKey =
            addressLoadService.findById(shippingAddressId).key

        val createOrderDomainDto =
            OrderCreator.createOrderDomainDto(
                customerKey = customer.key,
                businessSegmentKey = businessSegmentKey,
                orderKey = key,
                orderDate = orderDate,
                financingType = FinancingType.CASH,
                billingAddressKey = billingAddressKey,
                shippingAddressKey = shippingAddressKey,
                currency = currency,
                properties = mutableMapOf(PropertyKey.DIAGNOSES to diagnose),
                deliveryDate = orderDate.minusDays(14),
            )

        workflowApiService.startOrderWorkflow(tenantKey, SYSTEM_USER_KEY, createOrderDomainDto.copy(items = items))
    }

    private suspend fun addOrders() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND)
        val customerLisa = customerLoadService.findByTenantIdAndKey(tenantId, "1000189")
        val customerPeter = customerLoadService.findByTenantIdAndKey(tenantId, "1000098")

        val tenantConfig =
            tenantLoadService.findTenantConfigByTenantId(tenantId)
        val now = TimeProvider.nowDateInTimeZone(tenantConfig.getTimeZoneAsObject())

        val taxPerson = BigDecimal.ZERO

        addOrder(
            tenantKey = TENANT_KEY_MVZ_GESUND,
            customer = customerLisa,
            businessSegmentKey = "PRIVAT",
            key = UUID.randomUUID().toString(),
            orderDate = now.minusDays(3),
            currency = Currency.EUR,
            items = listOf(
                OrderCreator.createDebitOrderItem(
                    articleNumber = "1",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("4.66"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Beratung, auch telefonisch",
                    quantity = BigDecimal("2.3"),
                ),
                OrderCreator.createDebitOrderItem(
                    articleNumber = "250",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("2.33"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Blutentnahme mittels Spritze, Kanüle oder Katheter aus der Vene",
                    quantity = BigDecimal("2.3"),
                ),
            ),
            diagnose = "Veggie-Check",
        )

        addOrder(
            tenantKey = TENANT_KEY_MVZ_GESUND,
            customer = customerPeter,
            businessSegmentKey = "PRIVAT",
            key = UUID.randomUUID().toString(),
            orderDate = now.minusDays(1),
            currency = Currency.EUR,
            items = listOf(
                OrderCreator.createDebitOrderItem(
                    articleNumber = "1",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("4.66"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Beratung, auch telefonisch",
                    quantity = BigDecimal("2.3"),
                ),
                OrderCreator.createDebitOrderItem(
                    articleNumber = "5",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("4.66"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Symptombezogene Untersuchung",
                    quantity = BigDecimal("2.3"),
                ),
                OrderCreator.createDebitOrderItem(
                    articleNumber = "56",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("10.49"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Verweilen / Überwachung",
                    quantity = BigDecimal("1.8"),
                ),
                OrderCreator.createDebitOrderItem(
                    articleNumber = "263",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("5.25"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Subkutane Hyposensibilisierung",
                    quantity = BigDecimal("2.3"),
                ),
                OrderCreator.createDebitOrderItem(
                    articleNumber = "9910",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("1.25"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Sachkosten nach § 10",
                    quantity = BigDecimal("1.0"),
                ),
                OrderCreator.createDebitOrderItem(
                    articleNumber = "9962",
                    itemGroup = "GENERAL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("1.04"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Sachkosten nach § 10",
                    quantity = BigDecimal("1.0"),
                ),
            ),
            diagnose = "Rhinoconjunctivitis allergica",
        )
    }

    private suspend fun createPaymentConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND)

        createPaymentAccounts(tenantId)
    }

    private suspend fun createPaymentAccounts(
        tenantId: UUID,
    ) {
        paymentAccountService.createPaymentAccount(
            PaymentCreator.createPaymentAccount(
                tenantId = tenantId,
                accountId = "********************",
                name = "Commerzbank Bank Account",
                description = "Hauptkonto",
                bic = "COBADEFFXXX",
                accountHolder = "MVZ Gesund GmbH",
                paymentAccountType = PaymentAccountType.BANK_ACCOUNT,
                defaultAccount = true,
            ),
        )
    }

    private suspend fun createTranslations() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND)

        translationService.createTranslations(
            listOf(
                TranslationCreator.createTranslation(tenantId, GeneralDocumentTranslationKey.COPY.name, Language.DE, "KOPIE"),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.FINAL_INVOICE.name,
                    Language.DE,
                    "Rechnung",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.REVERSED.name,
                    Language.DE,
                    "Gutschrift",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.ARTICLE_NUMBER.name,
                    Language.DE,
                    "GO-Nr.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.DESCRIPTION.name,
                    Language.DE,
                    "Leistung",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.QTY.name,
                    Language.DE,
                    "Faktor",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.UNIT_PRICE.name,
                    Language.DE,
                    "Gebühr",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.TOTAL_PRICE.name,
                    Language.DE,
                    "Betrag",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.SUBTOTAL_AMOUNT.name,
                    Language.DE,
                    "Zwischensumme (netto):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.TAX_AMOUNTS.name,
                    Language.DE,
                    "Umsatzsteuer ({{TAX_RATE}}%):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.TOTAL_AMOUNT.name,
                    Language.DE,
                    "Gesamtbetrag:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.INVOICE_NUMBER.name,
                    Language.DE,
                    "Rechnungsnummer:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.INTRO_FINAL_INVOICE.name,
                    Language.DE,
                    "Wir erlauben uns, Ihnen die folgenden ärztlichen Leistungen in Rechnung zu stellen.\n\n" +
                        "Patient: {{PATIENT}}\n\n" +
                        "Diagnose(n): {{DIAGNOSES}}\n",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.INTRO_REVERSED.name,
                    Language.DE,
                    "Wir schreiben Ihnen gut:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.OUTRO_FINAL_INVOICE.name,
                    Language.DE,
                    "Der Rechnungsbetrag ist mit Zugang der Rechnung fällig. Wir weisen gemäß § 286 Abs. 3 BGB darauf hin, " +
                        "dass Sie auch ohne Mahnung automatisch in Verzug geraten, wenn Sie den Rechnungsbetrag nicht innerhalb " +
                        "von 30 Tagen nach Fälligkeit und Zugang bezahlen.\n" +
                        "\n" +
                        "Bitte überweisen Sie auf unser Bankkonto {{BANK_ACCOUNT}} und geben Sie " +
                        "bei der Zahlung die Rechnungsnummer an.\n" +
                        "\n" +
                        "Die aufgeführten Leistungen sind nach § 4 Nr. 14 UStG umsatzsteuerfrei.",
                ),

                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.OUTRO_REVERSED.name,
                    Language.DE,
                    "Sie erhalten innerhalb von 30 Tagen eine Rückerstattung, falls sich ein Guthaben auf Ihrem Konto ergibt.\n" +
                        "\n" +
                        "Die aufgeführten Leistungen sind nach § 4 Nr. 14 UStG umsatzsteuerfrei.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.INVOICE_DATE.name,
                    Language.DE,
                    "Rechnungsdatum:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.DELIVERY_DATE.name,
                    Language.DE,
                    "Behandlungsdatum:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.RELATED_DOCUMENT.name,
                    Language.DE,
                    "Originalbeleg:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.RELATED_DOCUMENT_DATE.name,
                    Language.DE,
                    "Originalbelegsdatum:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.CUSTOMER_NUMBER.name,
                    Language.DE,
                    "Kundennummer:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.CEO.name,
                    Language.DE,
                    "Geschäftsführer: Peter Gesund",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.TELEPHONE.name,
                    Language.DE,
                    "Telefon: 0123 456789",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.EMAIL.name,
                    Language.DE,
                    "E-Mail: <EMAIL>",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    MvzGesundDocumentToPdfMapping.MvzGesundTranslationKey.VAT_ID.name,
                    Language.DE,
                    "USt-IdNr.",
                ),

                TranslationCreator.createTranslation(
                    tenantId,
                    EmailTranslationKey.EMAIL_SUBJECT.name,
                    Language.DE,
                    "Rechnung für die Behandlung {{ORDER_KEY}}",
                    TranslationType.EMAIL,
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    EmailTranslationKey.EMAIL_BODY.name,
                    Language.DE,
                    "Sehr geehrte/r {{CUSTOMER_NAME}},\n\n" +
                        "anbei finden Sie die Rechnung für Ihre Behandlung {{ORDER_KEY}}.\n\n" +
                        "Bei Fragen stehen wir Ihnen gerne zur Verfügung.\n\n" +
                        "Mit freundlichen Grüßen,\n" +
                        "Ihr MVZ Gesund Team",
                    TranslationType.EMAIL,
                ),
            ),
        )
    }

    private suspend fun createBookingkeepingConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_MVZ_GESUND)

        createChartOfAccounts(tenantId)
        createBookingRules(tenantId)
    }

    private suspend fun createChartOfAccounts(
        tenantId: UUID,
    ) {
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2020,
                name = "Deutsche Bank Account",
                description = "",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2011,
                name = "Bank Clearing Account",
                description = "Account for Payment Clearing",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3200,
                name = "Output VAT Payable",
                description = "",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5050,
                name = "Discounts",
                description = "Discounts i.e. loyalty bonus, finance",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5000,
                name = "Panel Turnover",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2010,
                name = "Market Bank Account",
                description = "Market Bank Account for Deposits arriving from the market",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
    }

    private suspend fun createBookingRules(
        tenantId: UUID,
    ) {
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "General group booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Taxes booking",
                bookingRuleType = BookingRuleType.TAX_CODE,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                taxCodes = "*",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 3200,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Incoming payment booking",
                bookingRuleType = BookingRuleType.PAYMENT_TRANSACTION,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                bankAccountNumbers = "*",
                debitAccountNumber = 2010,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Payout booking",
                bookingRuleType = BookingRuleType.PAYMENT_TRANSACTION,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                bankAccountNumbers = "*",
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 2010,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Payout assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetTypes = TargetType.ORDER.name,
                debitAccountNumber = 3000,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Final customer payment assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetTypes = PaymentAssignmentTargetType.FINAL.name,
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 3000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Final customer payout assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetTypes = PaymentAssignmentTargetType.REVERSED.name,
                debitAccountNumber = 3000,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
    }

    private suspend fun createCucumberNotificationApiReceiver() {
        notificationApiReceiverService.createNotificationApiReceiver(
            tenantKey = TENANT_KEY_MVZ_GESUND,
            userKey = SYSTEM_USER_KEY,
            notificationApiReceiverDomainDto = NotificationReceiverCreator.createCucumberNotificationReceiver(),
        )
    }
}
