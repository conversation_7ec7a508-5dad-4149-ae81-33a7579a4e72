package com.klosesoft.billingsolution.databasefiller.tenant

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_ID
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.SYSTEM_USER_KEY
import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants.TENANT_KEY_SUNPOWER
import com.klosesoft.billingsolution.common.utils.TimeProvider
import com.klosesoft.billingsolution.databasefiller.creator.BookkeepingCreator
import com.klosesoft.billingsolution.databasefiller.creator.CustomerCreator
import com.klosesoft.billingsolution.databasefiller.creator.NotificationReceiverCreator
import com.klosesoft.billingsolution.databasefiller.creator.OrderCreator
import com.klosesoft.billingsolution.databasefiller.creator.PaymentCreator
import com.klosesoft.billingsolution.databasefiller.creator.ReportConfigCreator
import com.klosesoft.billingsolution.databasefiller.creator.TenantCreator
import com.klosesoft.billingsolution.databasefiller.creator.TranslationCreator
import com.klosesoft.billingsolution.domain.logic.api.job.report.ReportJob
import com.klosesoft.billingsolution.domain.logic.api.service.WorkflowApiService
import com.klosesoft.billingsolution.domain.logic.service.RoundingUtil
import com.klosesoft.billingsolution.domain.logic.service.billing.TranslationService
import com.klosesoft.billingsolution.domain.logic.service.billing.mapping.SunpowerDocumentToPdfMapping
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingAccountService
import com.klosesoft.billingsolution.domain.logic.service.bookkeeping.BookingRuleService
import com.klosesoft.billingsolution.domain.logic.service.common.AddressService
import com.klosesoft.billingsolution.domain.logic.service.common.BusinessSegmentService
import com.klosesoft.billingsolution.domain.logic.service.common.CustomerService
import com.klosesoft.billingsolution.domain.logic.service.common.NotificationApiReceiverService
import com.klosesoft.billingsolution.domain.logic.service.common.TenantService
import com.klosesoft.billingsolution.domain.logic.service.common.WorkflowDefinitionService
import com.klosesoft.billingsolution.domain.logic.service.payment.PaymentAccountService
import com.klosesoft.billingsolution.domain.logic.service.user.RoleService
import com.klosesoft.billingsolution.domain.logic.service.user.UserService
import com.klosesoft.billingsolution.domain.model.dto.AddressDomainDto
import com.klosesoft.billingsolution.domain.model.dto.OrderItemRequestDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.AddressType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.BookingRuleType
import com.klosesoft.billingsolution.domain.model.valueobject.Currency
import com.klosesoft.billingsolution.domain.model.valueobject.DebitCreditIndicator
import com.klosesoft.billingsolution.domain.model.valueobject.EmailTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.domain.model.valueobject.FinancingType
import com.klosesoft.billingsolution.domain.model.valueobject.GeneralDocumentTranslationKey
import com.klosesoft.billingsolution.domain.model.valueobject.Language
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAccountType
import com.klosesoft.billingsolution.domain.model.valueobject.PaymentAssignmentTargetType
import com.klosesoft.billingsolution.domain.model.valueobject.ReportFormat
import com.klosesoft.billingsolution.domain.model.valueobject.ReportInterval
import com.klosesoft.billingsolution.domain.model.valueobject.ReportType
import com.klosesoft.billingsolution.domain.model.valueobject.Right
import com.klosesoft.billingsolution.domain.model.valueobject.TargetType
import com.klosesoft.billingsolution.domain.model.valueobject.TaxCode
import com.klosesoft.billingsolution.domain.model.valueobject.TranslationType
import com.klosesoft.billingsolution.domain.model.valueobject.WorkflowType
import com.klosesoft.billingsolution.persistence.model.entity.Customer
import com.klosesoft.billingsolution.persistence.model.entity.Role
import com.klosesoft.billingsolution.persistence.service.AddressLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.RoleLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import com.klosesoft.billingsolution.workflow.WorkflowDeploymentService
import org.springframework.core.env.Environment
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Component
import java.io.IOException
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Locale
import java.util.UUID

@Component
class SunpowerInitializer(
    private val tenantService: TenantService,
    private val tenantLoadService: TenantLoadService,
    private val customerLoadService: CustomerLoadService,
    private val businessSegmentService: BusinessSegmentService,
    private val userService: UserService,
    private val userLoadService: UserLoadService,
    private val customerService: CustomerService,
    private val workflowApiService: WorkflowApiService,
    private val addressLoadService: AddressLoadService,
    private val notificationApiReceiverService: NotificationApiReceiverService,
    private val bookingAccountService: BookingAccountService,
    private val bookingRuleService: BookingRuleService,
    private val environment: Environment,
    private val roleLoadService: RoleLoadService,
    private val roleService: RoleService,
    private val paymentAccountService: PaymentAccountService,
    private val reportConfigCreator: ReportConfigCreator,
    private val reportJob: ReportJob,
    private val translationService: TranslationService,
    private val resourceLoader: ResourceLoader,
    private val addressService: AddressService,
    private val workflowDeploymentService: WorkflowDeploymentService,
    private val workflowDefinitionService: WorkflowDefinitionService,
) {

    suspend fun setupTenant() {
        createTenant()
        createBusinessSegments()
        createTenantWebPortalUsers()
        createBookingkeepingConfig()
        createPaymentConfig()
        createTranslations()

        if (environment.activeProfiles.contains("demo")) {
            addCustomers()
            addOrders()
            createReports()
        }

        if (environment.activeProfiles.contains("cucumber")) {
            createCucumberNotificationApiReceiver()
        }
    }

    private suspend fun createReports() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_SUNPOWER).id
        reportConfigCreator.createConfig(tenantId, ReportType.PAYMENT, ReportFormat.CSV, ReportInterval.DAILY, "CET")
        reportConfigCreator.createConfig(tenantId, ReportType.SUBLEDGER, ReportFormat.CSV, ReportInterval.DAILY, "CET")
        reportConfigCreator.createConfig(tenantId, ReportType.APPROVAL, ReportFormat.CSV, ReportInterval.WEEKLY, "CET")

        reportJob.generateSubledgerReport()
        reportJob.generatePaymentReport()
        reportJob.generateApprovalReport()
    }

    private suspend fun createTenantWebPortalUsers() {
        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_SUNPOWER).id

        createRoles(tenantId)
        createUserRoles(tenantId)
        createRoleRights(tenantId)
    }

    private suspend fun createRoles(
        tenantId: UUID,
    ) {
        roleService.createRole(createRole("Read-only", tenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Full Access", tenantId), emptyList(), SYSTEM_USER_ID)
        roleService.createRole(createRole("Administration", tenantId), emptyList(), SYSTEM_USER_ID)
    }

    private suspend fun createRole(
        name: String,
        tenantId: UUID,
    ) = Role(
        name = name,
        createdBy = SYSTEM_USER_ID,
        lastModifiedBy = SYSTEM_USER_ID,
        key = name,
        tenantId = tenantId,
    )

    private suspend fun createUserRoles(
        tenantId: UUID,
    ) {
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)
        setRolesForUser("<EMAIL>", listOf("Full Access", "Administration"), tenantId)

        setRolesForUser("<EMAIL>", listOf("Read-only"), tenantId)
    }

    private suspend fun setRolesForUser(
        key: String,
        roleKeys: List<String>,
        tenantUUID: UUID,
    ) {
        val user = userLoadService.findByKey(key)

        val roles = mutableListOf<Role>()

        roleKeys.forEach { roleKey ->
            val role = roleLoadService.findByTenantIdAndKey(tenantUUID, roleKey)
            roles.add(role)
        }

        userService.updateUser(user, roles, SYSTEM_USER_ID)
    }

    private suspend fun createRoleRights(
        tenantId: UUID,
    ) {
        addReadOnlyRights(tenantId)
        addFullAccessRights(tenantId)
        addAdminRights(tenantId)
    }

    private suspend fun addReadOnlyRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Read-only")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addFullAccessRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Full Access")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.READ || it.category == Right.Category.WRITE
            },
            SYSTEM_USER_ID,
        )
    }

    private suspend fun addAdminRights(
        tenantId: UUID,
    ) {
        val role = roleLoadService.findByTenantIdAndKey(tenantId, "Administration")
        roleService.updateRole(
            role,
            Right.entries.filter {
                it.category == Right.Category.ADMIN || it.category == Right.Category.INTERNAL
            },
            SYSTEM_USER_ID,
        )
    }

    private fun getLogo(): ByteArray {
        val logoResource = resourceLoader.getResource("classpath:/logos/logo-sunpower.png")

        if (logoResource.exists()) {
            try {
                return logoResource.contentAsByteArray
            } catch (e: IOException) {
                throw IllegalStateException("error while reading logo", e)
            }
        } else {
            throw IllegalStateException("logo was not found")
        }
    }

    private suspend fun createBusinessSegments() {
        val logo = getLogo()

        val originatorAddress = addressService.createAddress(
            TENANT_KEY_SUNPOWER,
            SYSTEM_USER_KEY,
            AddressDomainDto(
                UUID.randomUUID().toString(),
                companyName = "Sunpower GmbH",
                street = "Sonnenstr.",
                houseNumber = "15",
                city = "Hamburg",
                country = "DE",
                postalCode = "21266",
                mailAddress = "",
            ),
        )

        val tenantId = tenantLoadService.findTenantByKey(TENANT_KEY_SUNPOWER).id
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantId,
                "BENQ",
                "'BENQ-'&\$year&'-'&\$numberRange(100000,500000)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "DE783451789",
            ),
        )
        businessSegmentService.createBusinessSegment(
            TenantCreator.createBusinessSegment(
                tenantId,
                "LG",
                "'LG-'&\$year&'-'&\$numberRange(500001,999999)",
                logo,
                originatorAddressId = originatorAddress.id,
                vatId = "DE783451789",
            ),
        )
    }

    protected suspend fun createTenant() {
        val tenantSunpower = tenantService.createTenant(TenantCreator.createActiveTenant(TENANT_KEY_SUNPOWER))
        tenantService.createTenantConfig(
            TenantCreator.createTenantConfig(
                tenantId = tenantSunpower.id,
                appClientId = "6m8i610ovmvlokunu7vflk6o31",
                orderWorkflow = BillingSolutionConstants.STANDARD_ORDER_WORKFLOW,
                ledgerCurrency = Currency.EUR,
                features = listOf(Feature.ENABLE_BALANCE_CASE),
                locale = Locale.of("de", "DE"),
                theme = "sunpower",
                timeZone = "CET",
                defaultLanguage = Language.EN,
                defaultTaxRate = BigDecimal("19.00"),
            ),
        )

        workflowDeploymentService.deployWorkflows()
        // Note: Standard workflows are deployed by WorkflowDeploymentService
        // Custom workflow definitions are created through the web portal
    }



    private suspend fun addCustomers() {
        customerService.createCustomer(
            TENANT_KEY_SUNPOWER,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                "1000098",
                "Peter",
                "Schmidt",
                Language.DE,
                city = "Ulm",
                country = "DE",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_SUNPOWER,
            SYSTEM_USER_KEY,
            CustomerCreator.createPerson(
                "1000189",
                "Lisa",
                "Meier",
                Language.DE,
                city = "Hamburg",
                country = "DE",
            ),
        )

        customerService.createCustomer(
            TENANT_KEY_SUNPOWER,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany(
                "1000019",
                "Lufthansa AG",
                Language.DE,
                city = "Frankfurt",
                country = "DE",
            ),
        )
        customerService.createCustomer(
            TENANT_KEY_SUNPOWER,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany(
                "1000039",
                "REWE Group",
                Language.DE,
                city = "Köln",
                country = "DE",
            ),
        )
        customerService.createCustomer(
            TENANT_KEY_SUNPOWER,
            SYSTEM_USER_KEY,
            CustomerCreator.createCompany(
                "1000001",
                "Dreamland AG",
                Language.EN,
                city = "Köln",
                country = "DE",
            ),
        )
    }

    suspend fun addOrder(
        tenantKey: String,
        customer: Customer,
        businessSegmentKey: String,
        key: String,
        orderDate: LocalDate,
        currency: Currency,
        items: List<OrderItemRequestDomainDto>,
    ) {
        val shippingAddressId =
            addressLoadService.findLatestCustomerAddress(customer.tenantId, customer.id, AddressType.SHIPPING).addressId

        val billingAddressId =
            addressLoadService.findLatestCustomerAddress(customer.tenantId, customer.id, AddressType.BILLING)
                .addressId

        val billingAddressKey: String = addressLoadService.findById(billingAddressId).key

        val shippingAddressKey =
            addressLoadService.findById(shippingAddressId).key

        val totalNetSum = items.sumOf { RoundingUtil.round(it.unitNetAmount * it.quantity, currency) }

        val createOrderDomainDto =
            OrderCreator.createOrderDomainDto(
                customerKey = customer.key,
                businessSegmentKey = businessSegmentKey,
                orderKey = key,
                orderDate = orderDate,
                financingType = FinancingType.CASH,
                billingAddressKey = billingAddressKey,
                shippingAddressKey = shippingAddressKey,
                currency = currency,
                agreedDepositAmount = RoundingUtil.round(totalNetSum / BigDecimal("10.00"), currency),
            )

        workflowApiService.startOrderWorkflow(tenantKey, SYSTEM_USER_KEY, createOrderDomainDto.copy(items = items))
    }

    private suspend fun addOrders() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_SUNPOWER)
        val customerLufthansa = customerLoadService.findByTenantIdAndKey(tenantId, "1000019")
        val customerLisa = customerLoadService.findByTenantIdAndKey(tenantId, "1000189")
        val customerRewe = customerLoadService.findByTenantIdAndKey(tenantId, "1000039")
        val customerPeter = customerLoadService.findByTenantIdAndKey(tenantId, "1000098")
        val customerAirflight = customerLoadService.findByTenantIdAndKey(tenantId, "1000001")

        val tenantConfig =
            tenantLoadService.findTenantConfigByTenantId(tenantId)
        val now = TimeProvider.nowDateInTimeZone(tenantConfig.getTimeZoneAsObject())

        val taxPerson = BigDecimal.ZERO
        val taxCompany = BigDecimal.valueOf(0.19)

        addOrder(
            TENANT_KEY_SUNPOWER,
            customerLufthansa,
            "BENQ",
            UUID.randomUUID().toString(),
            now,
            Currency.EUR,
            listOf(
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PLANNING",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("1500.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Anlagenplanung",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PANEL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("312.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "BenQ SunForte PM096B00-330 W",
                    quantity = BigDecimal("150.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "BATTERY",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("678.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Huawei Speicher System 10kW/10KWh (3ph LiFePo4)",
                    quantity = BigDecimal("5.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "OPTIONS",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("899.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Unterkonstruktion, Schrägdach/Ziegeldach",
                    quantity = BigDecimal("5.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "INSTALLATION",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("12000.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Installation",
                    quantity = BigDecimal("1.00"),
                ),
            ),
        )
        addOrder(
            TENANT_KEY_SUNPOWER,
            customerLisa,
            "BENQ",
            UUID.randomUUID().toString(),
            now.minusDays(3),
            Currency.EUR,
            listOf(
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PLANNING",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("500.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Anlagenplanung",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PANEL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("295.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "BenQ SunForte PM096B00-320 W",
                    quantity = BigDecimal("20.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "BATTERY",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("872.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Huawei Speicher System 10kW/10KWh (3ph LiFePo4)",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "OPTIONS",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("899.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Unterkonstruktion, Schrägdach/Ziegeldach",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "INSTALLATION",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("9000.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Installation",
                    quantity = BigDecimal("1.00"),
                ),
            ),
        )
        addOrder(
            TENANT_KEY_SUNPOWER,
            customerRewe,
            "BENQ",
            UUID.randomUUID().toString(),
            now.minusDays(1),
            Currency.EUR,
            listOf(
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PLANNING",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("1500.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Anlagenplanung",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PANEL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("345.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "BenQ SunForte PM096B00-335 W",
                    quantity = BigDecimal("58.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "BATTERY",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("678.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Huawei Speicher System 10kW/10KWh (3ph LiFePo4)",
                    quantity = BigDecimal("3.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "OPTIONS",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("499.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Unterkonstruktion, Schrägdach/Ziegeldach",
                    quantity = BigDecimal("3.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "INSTALLATION",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("10500.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Installation",
                    quantity = BigDecimal("1.00"),
                ),
            ),
        )

        addOrder(
            TENANT_KEY_SUNPOWER,
            customerAirflight,
            "LG",
            UUID.randomUUID().toString(),
            now,
            Currency.EUR,
            listOf(
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PLANNING",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("2000.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Anlagenplanung",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PANEL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("202.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "LG Neon 2 Black LG335N1K-V5",
                    quantity = BigDecimal("240.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "BATTERY",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("499.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Huawei Speicher System 10kW/10KWh (3ph LiFePo4)",
                    quantity = BigDecimal("10.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "OPTIONS",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("799.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Unterkonstruktion, Schrägdach/Ziegeldach",
                    quantity = BigDecimal("10.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "INSTALLATION",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("20500.00"),
                    currency = Currency.EUR,
                    taxRate = taxCompany,
                    taxCode = TaxCode.D7,
                    name = "Installation",
                    quantity = BigDecimal("1.00"),
                ),
            ),
        )
        addOrder(
            TENANT_KEY_SUNPOWER,
            customerPeter,
            "LG",
            UUID.randomUUID().toString(),
            now.minusDays(1),
            Currency.EUR,
            listOf(
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PLANNING",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("500.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Anlagenplanung",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "PANEL",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("309.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "LG Neon R Prime LG360Q1K-V5",
                    quantity = BigDecimal("2.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "BATTERY",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("872.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Huawei Speicher System 10kW/10KWh (3ph LiFePo4)",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "OPTIONS",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("899.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Unterkonstruktion, Schrägdach/Ziegeldach",
                    quantity = BigDecimal("1.00"),
                ),
                OrderCreator.createDebitOrderItem(
                    itemGroup = "INSTALLATION",
                    key = UUID.randomUUID().toString(),
                    unitNetPrice = BigDecimal("9000.00"),
                    currency = Currency.EUR,
                    taxRate = taxPerson,
                    taxCode = TaxCode.EMPTY,
                    name = "Installation",
                    quantity = BigDecimal("1.00"),
                ),
            ),
        )
    }

    private suspend fun createPaymentConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_SUNPOWER)

        createPaymentAccounts(tenantId)
    }

    private suspend fun createPaymentAccounts(
        tenantId: UUID,
    ) {
        paymentAccountService.createPaymentAccount(
            PaymentCreator.createPaymentAccount(
                tenantId = tenantId,
                accountId = "********************",
                name = "Commerzbank Bank Account",
                description = "For Deposit Payments",
                bic = "COBADEFFXXX",
                accountHolder = "Sunpower GmbH",
                paymentAccountType = PaymentAccountType.BANK_ACCOUNT,
                defaultAccount = true,
            ),
        )
    }

    private suspend fun createTranslations() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_SUNPOWER)

        translationService.createTranslations(
            listOf(
                TranslationCreator.createTranslation(tenantId, GeneralDocumentTranslationKey.COPY.name, Language.EN, "COPY"),
                TranslationCreator.createTranslation(
                    tenantId,
                    GeneralDocumentTranslationKey.DEPOSIT.name,
                    Language.EN,
                    "minus deposit (net)",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DEPOSIT_INVOICE.name,
                    Language.EN,
                    "Deposit Invoice",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.FINAL_INVOICE.name,
                    Language.EN,
                    "Final Invoice",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.REVERSED.name,
                    Language.EN,
                    "Credit Note",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DESCRIPTION.name,
                    Language.EN,
                    "Description",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.QTY.name,
                    Language.EN,
                    "Qty",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.UNIT_PRICE.name,
                    Language.EN,
                    "Unit Price",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TOTAL_PRICE.name,
                    Language.EN,
                    "Total Price",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.SUBTOTAL_AMOUNT.name,
                    Language.EN,
                    "Subtotal (net):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DEPOSIT_AMOUNT.name,
                    Language.EN,
                    "Deposit Amount (10%) (net):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TAX_AMOUNTS.name,
                    Language.EN,
                    "Tax Amounts ({{TAX_RATE}}%):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TOTAL_AMOUNT.name,
                    Language.EN,
                    "Total Amount:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TOTAL_AMOUNT_DEPOSIT.name,
                    Language.EN,
                    "Total Amount Deposit:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INVOICE_NUMBER.name,
                    Language.EN,
                    "Invoice Number:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_DEPOSIT_INVOICE_PERSON.name,
                    Language.EN,
                    "By accepting the offer, the client agrees to meet the conditions for the application" +
                        " of 0% VAT in accordance with §12 paragraph 3 of the UStG.\n\n" +
                        "We request a deposit for the following services:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_DEPOSIT_INVOICE_COMPANY.name,
                    Language.EN,
                    "We request a deposit for the following services:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_FINAL_INVOICE.name,
                    Language.EN,
                    "We allow ourselves to invoice you for the following services:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_REVERSED.name,
                    Language.EN,
                    "We allow ourselves to credit you:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.OUTRO_DEPOSIT_INVOICE.name,
                    Language.EN,
                    "Payable until {{PAYMENT_DUE_DATE}} to our bank account {{BANK_ACCOUNT}}.\n" +
                        "\n" +
                        "Please be sure to specify the invoice number when making a payment." +
                        "\n\n" +
                        "We will start with processing the order after receipt of the deposit." +
                        "\n\n" +
                        "The listed invoice items including the prices are preliminary and may still change.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.OUTRO_FINAL_INVOICE.name,
                    Language.EN,
                    "Payable until {{PAYMENT_DUE_DATE}} to our bank account {{BANK_ACCOUNT}}.\n" +
                        "\n" +
                        "Please be sure to specify the invoice number when making a payment.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.OUTRO_REVERSED.name,
                    Language.EN,
                    "You will receive a refund within 30 days if there is a balance on your account.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INVOICE_DATE.name,
                    Language.EN,
                    "Invoice Date:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DELIVERY_DATE.name,
                    Language.EN,
                    "Delivery Date:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.RELATED_DOCUMENT.name,
                    Language.EN,
                    "Original Document:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.RELATED_DOCUMENT_DATE.name,
                    Language.EN,
                    "Original Document Date:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.CUSTOMER_NUMBER.name,
                    Language.EN,
                    "Customer Number:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.CEO.name,
                    Language.EN,
                    "CEO",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TELEPHONE.name,
                    Language.EN,
                    "Telephone",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.EMAIL.name,
                    Language.EN,
                    "Email",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.VAT_ID.name,
                    Language.EN,
                    "VAT ID",
                ),

                TranslationCreator.createTranslation(
                    tenantId,
                    EmailTranslationKey.EMAIL_SUBJECT.name,
                    Language.EN,
                    "Invoice for Order {{ORDER_KEY}}",
                    TranslationType.EMAIL,
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    EmailTranslationKey.EMAIL_BODY.name,
                    Language.EN,
                    "Dear {{CUSTOMER_NAME}},\n\n" +
                        "Please find attached the invoice for your order {{ORDER_KEY}}.\n\n" +
                        "If you have any questions, please do not hesitate to contact us.\n\n" +
                        "Best regards,\n" +
                        "Your Sunpower Team",
                    TranslationType.EMAIL,
                ),
            ),
        )

        translationService.createTranslations(
            listOf(
                TranslationCreator.createTranslation(tenantId, GeneralDocumentTranslationKey.COPY.name, Language.DE, "KOPIE"),
                TranslationCreator.createTranslation(
                    tenantId,
                    GeneralDocumentTranslationKey.DEPOSIT.name,
                    Language.DE,
                    "abzüglich Anzahlung (netto)",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DEPOSIT_INVOICE.name,
                    Language.DE,
                    "Anzahlungsrechnung",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.FINAL_INVOICE.name,
                    Language.DE,
                    "Rechnung",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.REVERSED.name,
                    Language.DE,
                    "Gutschrift",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DESCRIPTION.name,
                    Language.DE,
                    "Beschreibung",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.QTY.name,
                    Language.DE,
                    "Menge",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.UNIT_PRICE.name,
                    Language.DE,
                    "Stückpreis",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TOTAL_PRICE.name,
                    Language.DE,
                    "Gesamtpreis",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.SUBTOTAL_AMOUNT.name,
                    Language.DE,
                    "Zwischensumme (netto):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DEPOSIT_AMOUNT.name,
                    Language.DE,
                    "Anzahlung 10% (netto):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TAX_AMOUNTS.name,
                    Language.DE,
                    "Umsatzsteuer ({{TAX_RATE}}%):",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TOTAL_AMOUNT.name,
                    Language.DE,
                    "Gesamtbetrag:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TOTAL_AMOUNT_DEPOSIT.name,
                    Language.DE,
                    "Gesamtbetrag Anzahlung:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INVOICE_NUMBER.name,
                    Language.DE,
                    "Rechnungsnummer:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_DEPOSIT_INVOICE_PERSON.name,
                    Language.DE,
                    "Die Bedingungen zur Erfüllung der 0% Umsatzsteuer gem. §12 Abs. 3 UStG sichert der Auftraggeber mit " +
                        "Annahme des Angebotes zu.\n\n" +
                        "Wir erlauben uns, für folgende Leistungen eine Anzahlung zu fordern:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_DEPOSIT_INVOICE_COMPANY.name,
                    Language.DE,
                    "Wir erlauben uns, für folgende Leistungen eine Anzahlung zu fordern:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_FINAL_INVOICE.name,
                    Language.DE,
                    "Wir erlauben uns, Ihnen die folgenden Leistungen in Rechnung zu stellen:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INTRO_REVERSED.name,
                    Language.DE,
                    "Wir erlauben uns, Ihnen gutzuschreiben:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.OUTRO_DEPOSIT_INVOICE.name,
                    Language.DE,
                    "Zahlbar bis zum {{PAYMENT_DUE_DATE}} auf unser Bankkonto {{BANK_ACCOUNT}}.\n" +
                        "\n" +
                        "Bitte geben Sie bei der Zahlung die Rechnungsnummer an." + "\n\n" +
                        "Wir beginnen mit der Ausführung des Auftrages erst nach Zahlungseingang." + "\n\n" +
                        "Die aufgeführten Rechnungspositionen inklusive der Preise sind vorläufig und können sich noch ändern.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.OUTRO_FINAL_INVOICE.name,
                    Language.DE,
                    "Zahlbar bis zum {{PAYMENT_DUE_DATE}} auf unser Bankkonto {{BANK_ACCOUNT}}.\n" +
                        "\n" +
                        "Bitte geben Sie bei der Zahlung die Rechnungsnummer an.",
                ),

                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.OUTRO_REVERSED.name,
                    Language.DE,
                    "Sie erhalten innerhalb von 30 Tagen eine Rückerstattung, falls sich ein Guthaben auf Ihrem Konto ergibt.",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.INVOICE_DATE.name,
                    Language.DE,
                    "Rechnungsdatum:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.DELIVERY_DATE.name,
                    Language.DE,
                    "Lieferdatum:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.RELATED_DOCUMENT.name,
                    Language.DE,
                    "Originalbeleg:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.RELATED_DOCUMENT_DATE.name,
                    Language.DE,
                    "Originalbelegsdatum:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.CUSTOMER_NUMBER.name,
                    Language.DE,
                    "Kundennummer:",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.CEO.name,
                    Language.DE,
                    "Geschäftsführer",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.TELEPHONE.name,
                    Language.DE,
                    "Telefon",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.EMAIL.name,
                    Language.DE,
                    "E-Mail",
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    SunpowerDocumentToPdfMapping.SunpowerTranslationKey.VAT_ID.name,
                    Language.DE,
                    "USt-IdNr.",
                ),

                TranslationCreator.createTranslation(
                    tenantId,
                    EmailTranslationKey.EMAIL_SUBJECT.name,
                    Language.DE,
                    "Rechnung für die Bestellung {{ORDER_KEY}}",
                    TranslationType.EMAIL,
                ),
                TranslationCreator.createTranslation(
                    tenantId,
                    EmailTranslationKey.EMAIL_BODY.name,
                    Language.DE,
                    "Sehr geehrte/r {{CUSTOMER_NAME}},\n\n" +
                        "anbei finden Sie die Rechnung für Ihre Bestellung {{ORDER_KEY}}.\n\n" +
                        "Bei Fragen stehen wir Ihnen gerne zur Verfügung.\n\n" +
                        "Mit freundlichen Grüßen,\n" +
                        "Ihr Sunpower Team",
                    TranslationType.EMAIL,
                ),
            ),
        )
    }

    private suspend fun createBookingkeepingConfig() {
        val tenantId = tenantLoadService.fetchTenantId(TENANT_KEY_SUNPOWER)

        createChartOfAccounts(tenantId)
        createBookingRules(tenantId)
    }

    private suspend fun createChartOfAccounts(
        tenantId: UUID,
    ) {
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2000,
                name = "JPMorgan Bank Account",
                description = "",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2020,
                name = "Deutsche Bank Account",
                description = "",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5200,
                name = "Delivery Turnover",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2011,
                name = "Bank Clearing Account",
                description = "Account for Payment Clearing",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3200,
                name = "Output VAT Payable",
                description = "",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5050,
                name = "Discounts",
                description = "Discounts i.e. loyalty bonus, finance",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5060,
                name = "Received Deposit",
                description = "Received deposits",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 5000,
                name = "Panel Turnover",
                description = "",
                bookingAccountType = BookingAccountType.INCOME,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 2010,
                name = "Market Bank Account",
                description = "Market Bank Account for Deposits arriving from the market",
                bookingAccountType = BookingAccountType.ASSET,
            ),
        )
        bookingAccountService.createBookingAccount(
            BookkeepingCreator.createBookingAccount(
                tenantId = tenantId,
                accountNumber = 3000,
                name = "Deposit Holding Account",
                description = "Deposits",
                bookingAccountType = BookingAccountType.LIABILITY,
            ),
        )
    }

    private suspend fun createBookingRules(
        tenantId: UUID,
    ) {
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "PANEL group booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "PANEL",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "General group booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "DEPOSIT invoice booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemCategories = "DEPOSIT",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5060,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "DEPOSIT booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                itemCategories = "DEPOSIT",
                debitAccountNumber = 5060,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 0,
                creditAccountIsCreditor = true,
                creditPostingKey = "01",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Delivery booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                itemGroups = "OPTIONS",
                itemArticleNumbers = "RDCHG",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 5200,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Discount booking",
                bookingRuleType = BookingRuleType.DOCUMENT_ITEM,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                itemGroups = "DISCOUNT",
                debitAccountNumber = 5050,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 0,
                creditAccountIsCreditor = true,
                creditPostingKey = "01",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Taxes booking",
                bookingRuleType = BookingRuleType.TAX_CODE,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                taxCodes = "*",
                debitAccountNumber = 0,
                debitAccountIsParty = true,
                debitPostingKey = "01",
                creditAccountNumber = 3200,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Incoming payment booking",
                bookingRuleType = BookingRuleType.PAYMENT_TRANSACTION,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                bankAccountNumbers = "*",
                debitAccountNumber = 2010,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Payout booking",
                bookingRuleType = BookingRuleType.PAYMENT_TRANSACTION,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                bankAccountNumbers = "*",
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 2010,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Deposit assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetTypes = TargetType.ORDER.name,
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 3000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Payout assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetTypes = TargetType.ORDER.name,
                debitAccountNumber = 3000,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Final customer payment assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.DEBIT,
                targetTypes = PaymentAssignmentTargetType.FINAL.name,
                debitAccountNumber = 2011,
                debitAccountIsParty = false,
                debitPostingKey = "40",
                creditAccountNumber = 3000,
                creditAccountIsCreditor = false,
                creditPostingKey = "50",
            ),
        )
        bookingRuleService.createBookingRule(
            BookkeepingCreator.createBookingRule(
                tenantId = tenantId,
                name = "Final customer payout assignment booking",
                bookingRuleType = BookingRuleType.PAYMENT_ASSIGNMENT,
                debitCreditIndicator = DebitCreditIndicator.CREDIT,
                targetTypes = PaymentAssignmentTargetType.REVERSED.name,
                debitAccountNumber = 3000,
                debitAccountIsParty = false,
                debitPostingKey = "50",
                creditAccountNumber = 2011,
                creditAccountIsCreditor = false,
                creditPostingKey = "40",
            ),
        )
    }

    private suspend fun createCucumberNotificationApiReceiver() {
        notificationApiReceiverService.createNotificationApiReceiver(
            tenantKey = TENANT_KEY_SUNPOWER,
            userKey = SYSTEM_USER_KEY,
            notificationApiReceiverDomainDto = NotificationReceiverCreator.createCucumberNotificationReceiver(),
        )
    }
}
