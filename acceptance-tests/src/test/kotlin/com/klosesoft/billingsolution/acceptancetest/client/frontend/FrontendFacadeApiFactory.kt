package com.klosesoft.billingsolution.acceptancetest.client.frontend

import com.klosesoft.billingsolution.common.utils.BillingSolutionConstants
import com.klosesoft.billingsolution.common.utils.authtypes.OAuth2WithClientIdAndSecretAuthorization
import com.klosesoft.billingsolution.network.client.ApiClientFactory
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.env.Environment
import org.springframework.stereotype.Service

@Service
class FrontendFacadeApiFactory(@Value("\${o2c.frontend.baseurl}") private val frontendUrl: String, environment: Environment) :
    ApiClientFactory(JacksonFrontendApiSerializer.jacksonObjectMapper, environment) {
    private val apis = mutableMapOf<String, FrontendFacadeApi>()

    private val logger = KotlinLogging.logger { }

    private val auths = mapOf(
        BillingSolutionConstants.TENANT_KEY_SUNPOWER to
            Pair("6m8i610ovmvlokunu7vflk6o31", "12afr6vc291ad4idd6e8iu60c8q915jea1ju4vnb3j6jlptdpf0s"),
        BillingSolutionConstants.TENANT_KEY_MVZ_GESUND to
            Pair("4c1dvp58l0a7d8lbqvckq2pc59", "7jbflc3bup93emrsekg2h6mqom6ds5c192413uc5rhkhs4geko2"),
        BillingSolutionConstants.TENANT_KEY_BIKESALE_UK to
            Pair("3rth10k720ihm8ip646j5nvv79", "ni4iplmpmf35duco6brfd1ek6kkq0ci6tf4uqmtcevhgem2ldi9"),
        BillingSolutionConstants.TENANT_KEY_VELOCITY_WHEELS to
            Pair("66hkh9vsshaum9cr5go2qd6oa", "65l86v2ihj3rhu7dbbmljnb4j5pb225iscc2omick460b0u07od"),
    )

    fun getTenantFacadeApiClient(
        key: String,
    ): FrontendFacadeApi {
        val concreteAuth = auths[key]!!

        val result =
            apis.getOrDefault(
                key = key,
                defaultValue =
                createApiClient(
                    baseUrl = frontendUrl,
                    auth =
                    OAuth2WithClientIdAndSecretAuthorization(
                        accessTokenUri = "https://auth.klosesoft.com/oauth2/token",
                        clientId = concreteAuth.first,
                        clientSecret = concreteAuth.second,
                        roles = listOf("https://auth.klosesoft.com/external-api"),
                    ),
                    logger = logger,
                ).createService(FrontendFacadeApi::class.java),
            )

        apis.putIfAbsent(key, result)
        return result
    }
}
