plugins {
    alias(libs.plugins.gradle.cucumber)
    alias(libs.plugins.gradle.docker.compose)
    alias(libs.plugins.gradle.cluecumber)
    application
    java
}

apply(plugin = "org.springframework.boot")
apply(plugin = "io.spring.dependency-management")

dockerCompose {
    useComposeFiles.add("docker/docker-compose.yml")
    dockerExecutable = "/usr/local/bin/docker"
}

dependencies {
    implementation(project(":common-utils"))
    implementation(project(":common-network"))
    implementation(project(":api-outgoing-notification-server"))
    implementation(project(":api-external-client"))

    implementation(libs.spring.boot.starter.webflux)
    implementation(libs.spring.boot.starter.validation)

    implementation(libs.jackson.module.kotlin)
    implementation(libs.bundles.impl.network)
    implementation(libs.bundles.impl.coroutines)

    testImplementation(libs.kotest.junit)
    testImplementation(libs.kotest.assertions)
    testImplementation(libs.kotest.property)
    testImplementation(libs.angus.mail)
    testImplementation(libs.mustang) {
        exclude(group = "org.dom4j", module = "dom4j")
    }
    testImplementation(libs.xmpbox)
    testImplementation(libs.preflight)
    testImplementation(libs.dom4j)
    testImplementation(libs.xmlunit)
    testImplementation(libs.spring.boot.starter.test)
    testImplementation(libs.bundles.test.cucumber)
    testImplementation(libs.pdfbox)
    testImplementation(libs.commons.io)
}

application {
    mainClass.set(if (project.hasProperty("mainClass")) project.property("mainClass") as String else "NULL")
}

cucumber {
    threads = (Runtime.getRuntime().availableProcessors().div(2).coerceAtLeast(1)).toString()
    glue = "classpath:com.klosesoft.billingsolution.acceptancetest"
    plugin =
        arrayOf(
            "pretty",
            "json:" + (
                project.properties["jsonResultFile"] as String?
                    ?: "${layout.buildDirectory.dir("results").get().asFile.path}/cucumber.json"
                ),
            "junit:${layout.buildDirectory.dir("test-results").get().asFile.path}/test/TEST-acceptance-tests.xml",
        )

    featurePath = project.properties["featurePath"] as String? ?: "src/test/resources/features"
    main = "io.cucumber.core.cli.Main"
}

cluecumberReports {
    sourceJsonReportDirectory = layout.buildDirectory.dir("results").get().asFile.path
    generatedHtmlReportDirectory = "$rootDir/${project.name}/report"
    customPageTitle = "Billing Solution Test Report"
}

tasks {
    val composeUp by getting {
        dependsOn(":app-frontend:jibDockerBuild")
    }

    val cucumber by getting {
        dependsOn(composeUp)
    }

    val cucumberTest by registering {
        dependsOn(cucumber)

//        finalizedBy(generateCluecumberReports, composeLogs, composeDownForced)
    }
}
