########## Webportal Default Users ###############

resource "aws_cognito_user" "florian_fittkau" {
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id
  username     = "<EMAIL>"
  password     = var.user_password

  attributes = {
    email          = "<EMAIL>"
    email_verified = true
  }
}

resource "aws_cognito_user" "paul_klose" {
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id
  username     = "<EMAIL>"
  password     = var.user_password

  attributes = {
    email          = "<EMAIL>"
    email_verified = true
  }
}

resource "aws_cognito_user" "julian_zaruba" {
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id
  username     = "<EMAIL>"
  password     = var.user_password

  attributes = {
    email          = "<EMAIL>"
    email_verified = true
  }
}

##############################

resource "aws_cognito_user_pool" "auth_user_pool" {
  name = "auth-user-pool"

  password_policy {
    minimum_length                   = 8
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = true
    require_uppercase                = true
    temporary_password_validity_days = 7
  }

  username_attributes = ["email"]

  schema {
    attribute_data_type      = "String"
    developer_only_attribute = false
    mutable                  = true
    name                     = "email"
    required                 = true

    string_attribute_constraints {
      min_length = 0
      max_length = 2048
    }
  }

  auto_verified_attributes = ["email"]

  admin_create_user_config {
    allow_admin_create_user_only = true
    invite_message_template {
      email_message = "Dear customer,<br><br>thank you for signing up to our service. Please login on <a href='https://billing.klosesoft.com'>https://billing.klosesoft.com</a> using the following credentials:<br><br>Username: {username}<br>Temporary password: {####}<br><br>Best regards,<br>Your Billing Solution Team"
      email_subject = "Welcome to Billing Solution!"
      sms_message   = "Please login on https://billing.klosesoft.com using:\n\nUsername: {username}\nPassword: {####}"
    }
  }
}

resource "aws_ses_email_identity" "no_reply_klosesoft" {
  email = "<EMAIL>"
}

resource "aws_cognito_user_pool_client" "webportal_public_client" {
  name         = "webportal-public-client"
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id

  explicit_auth_flows = [
    "ALLOW_USER_SRP_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]

  access_token_validity                = 1
  supported_identity_providers = ["COGNITO"]
  callback_urls = ["https://billing.klosesoft.com/callback"]
  logout_urls = ["https://billing.klosesoft.com/logout"]
  allowed_oauth_flows = ["code"]
  allowed_oauth_scopes = ["https://auth.klosesoft.com/webportal-api", "aws.cognito.signin.user.admin"]
  allowed_oauth_flows_user_pool_client = true
  generate_secret                      = false
}

resource "aws_acm_certificate" "auth_cert" {
  domain_name       = "auth.klosesoft.com"
  validation_method = "EMAIL"
  provider          = aws.useast1

  validation_option {
    domain_name       = "auth.klosesoft.com"
    validation_domain = "klosesoft.com"
  }

  lifecycle {
    create_before_destroy = true
  }
}

resource "aws_cognito_user_pool_domain" "auth_cert" {
  domain          = "auth.klosesoft.com"
  user_pool_id    = aws_cognito_user_pool.auth_user_pool.id
  certificate_arn = aws_acm_certificate.auth_cert.arn
}

resource "aws_cognito_resource_server" "auth_resource_server" {
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id
  identifier   = "https://auth.klosesoft.com"
  name         = "auth-klosesoft-com"

  scope {
    scope_name        = "webportal-api"
    scope_description = "webportal-api"
  }

  scope {
    scope_name        = "external-api"
    scope_description = "external-api"
  }
}

################## Tenants ####################

resource "aws_cognito_user_pool_client" "sunpower_client" {
  name         = "sunpower-client"
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id

  explicit_auth_flows = [
    "ALLOW_USER_SRP_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]

  access_token_validity                = 1
  supported_identity_providers = ["COGNITO"]
  allowed_oauth_flows = ["client_credentials"]
  allowed_oauth_scopes = ["https://auth.klosesoft.com/external-api"]
  allowed_oauth_flows_user_pool_client = true
  generate_secret                      = true
}

resource "aws_cognito_user_pool_client" "mvz_gesund_client" {
  name         = "mvz-gesund-client"
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id

  explicit_auth_flows = [
    "ALLOW_USER_SRP_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]

  access_token_validity                = 1
  supported_identity_providers = ["COGNITO"]
  allowed_oauth_flows = ["client_credentials"]
  allowed_oauth_scopes = ["https://auth.klosesoft.com/external-api"]
  allowed_oauth_flows_user_pool_client = true
  generate_secret                      = true
}

resource "aws_cognito_user_pool_client" "bikesale_uk_client" {
  name         = "bikesale-uk-client"
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id

  explicit_auth_flows = [
    "ALLOW_USER_SRP_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]

  access_token_validity                = 1
  supported_identity_providers = ["COGNITO"]
  allowed_oauth_flows = ["client_credentials"]
  allowed_oauth_scopes = ["https://auth.klosesoft.com/external-api"]
  allowed_oauth_flows_user_pool_client = true
  generate_secret                      = true
}

resource "aws_cognito_user_pool_client" "velocity_wheels_client" {
  name         = "velocity-wheels-client"
  user_pool_id = aws_cognito_user_pool.auth_user_pool.id

  explicit_auth_flows = [
    "ALLOW_USER_SRP_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]

  access_token_validity                = 1
  supported_identity_providers = ["COGNITO"]
  allowed_oauth_flows = ["client_credentials"]
  allowed_oauth_scopes = ["https://auth.klosesoft.com/external-api"]
  allowed_oauth_flows_user_pool_client = true
  generate_secret                      = true
}
