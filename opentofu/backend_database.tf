resource "aws_db_subnet_group" "db_subnet_group" {
  name       = "db_subnet_group"
  subnet_ids = [aws_subnet.db_subnet_1a.id, aws_subnet.db_subnet_1b.id]

  tags = {
    Name = "db_subnet_group"
  }
}

resource "aws_db_instance" "billing_solution_db" {
  allocated_storage               = 20
  storage_type                    = "gp3"
  engine                          = "postgres"
  engine_version                  = "16.8"
  instance_class                  = "db.t4g.micro"
  db_name                         = "billing_solution"
  username                        = "klosesoft"
  password                        = var.db_password
  parameter_group_name            = "default.postgres16"
  skip_final_snapshot             = true
  vpc_security_group_ids          = [aws_security_group.db_sg.id]
  db_subnet_group_name            = aws_db_subnet_group.db_subnet_group.name
  enabled_cloudwatch_logs_exports = ["postgresql", "upgrade"]

  // Enable encryption
  storage_encrypted = true
}
