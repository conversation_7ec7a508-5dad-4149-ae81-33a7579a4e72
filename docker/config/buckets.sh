#!/usr/bin/env bash

# Set AWS credentials for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=eu-central-1

# Wait for LocalStack to be ready
echo "Waiting for LocalStack to be ready..."
until aws --endpoint-url=http://localhost:4566 s3 ls > /dev/null 2>&1; do
    echo "LocalStack not ready yet, waiting..."
    sleep 2
done

echo "LocalStack is ready, creating S3 bucket..."

# Create S3 bucket using aws CLI with LocalStack endpoint
if aws --endpoint-url=http://localhost:4566 s3 mb s3://bucket; then
    echo "S3 bucket 'bucket' created successfully"
else
    echo "Failed to create S3 bucket or bucket already exists"
    # Check if bucket already exists
    if aws --endpoint-url=http://localhost:4566 s3 ls s3://bucket > /dev/null 2>&1; then
        echo "S3 bucket 'bucket' already exists"
    else
        echo "Error: S3 bucket creation failed"
        exit 1
    fi
fi

echo "S3 setup completed"
