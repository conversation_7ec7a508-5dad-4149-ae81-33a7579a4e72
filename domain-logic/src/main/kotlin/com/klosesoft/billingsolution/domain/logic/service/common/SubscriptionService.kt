package com.klosesoft.billingsolution.domain.logic.service.common

import com.klosesoft.billingsolution.domain.logic.service.SubscriptionHistoryService
import com.klosesoft.billingsolution.domain.logic.service.SubscriptionItemService
import com.klosesoft.billingsolution.domain.model.dto.SubscriptionDomainDto
import com.klosesoft.billingsolution.domain.model.valueobject.ProcessingStatus
import com.klosesoft.billingsolution.domain.model.valueobject.SubscriptionStatus
import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import com.klosesoft.billingsolution.persistence.model.entity.SubscriptionHistoryEntry
import com.klosesoft.billingsolution.persistence.service.BusinessSegmentLoadService
import com.klosesoft.billingsolution.persistence.service.CustomerLoadService
import com.klosesoft.billingsolution.persistence.service.SubscriptionHistoryEntryLoadService
import com.klosesoft.billingsolution.persistence.service.SubscriptionLoadService
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import com.klosesoft.billingsolution.persistence.service.UserLoadService
import io.github.oshai.kotlinlogging.KotlinLogging
import kotlinx.coroutines.reactive.awaitSingle
import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.util.UUID

@Service
class SubscriptionService(
    private val tenantLoadService: TenantLoadService,
    private val userLoadService: UserLoadService,
    private val customerLoadService: CustomerLoadService,
    private val businessSegmentLoadService: BusinessSegmentLoadService,
    private val subscriptionLoadService: SubscriptionLoadService,
    private val subscriptionHistoryService: SubscriptionHistoryService,
    private val subscriptionHistoryEntryLoadService: SubscriptionHistoryEntryLoadService,
    private val subscriptionItemService: SubscriptionItemService,
    private val r2dbcEntityTemplate: R2dbcEntityTemplate,
) {
    val logger = KotlinLogging.logger {}

    suspend fun createSubscription(
        tenantKey: String,
        userKey: String,
        subscriptionDomainDto: SubscriptionDomainDto,
    ): Subscription {
        logger.info { "Creating subscription '${subscriptionDomainDto.key}' for tenant '$tenantKey'" }

        val tenantId = tenantLoadService.fetchTenantId(tenantKey)
        val userId = userLoadService.fetchUserId(userKey)
        val customer = customerLoadService.findByTenantIdAndKey(tenantId, subscriptionDomainDto.customerKey)
        val businessSegment = businessSegmentLoadService.findBusinessSegment(tenantId, subscriptionDomainDto.businessSegmentKey)

        val subscription = Subscription(
            tenantId = tenantId,
            key = subscriptionDomainDto.key,
            name = subscriptionDomainDto.name,
            description = subscriptionDomainDto.description,
            customerId = customer.id,
            businessSegmentId = businessSegment.id,
            status = subscriptionDomainDto.status,
            frequency = subscriptionDomainDto.frequency,
            amount = subscriptionDomainDto.amount,
            currency = subscriptionDomainDto.currency,
            startDate = subscriptionDomainDto.startDate,
            endDate = subscriptionDomainDto.endDate,
            nextBillingDate = subscriptionDomainDto.nextBillingDate,
            properties = subscriptionDomainDto.properties,
            createdBy = userId,
            lastModifiedBy = userId,
        )

        val createdSubscription = r2dbcEntityTemplate.insert(subscription).awaitSingle()

        // Add subscription items if provided
        if (subscriptionDomainDto.items.isNotEmpty()) {
            subscriptionDomainDto.items.forEachIndexed { index, item ->
                subscriptionItemService.addSubscriptionItem(
                    userId = userId,
                    subscription = createdSubscription,
                    subscriptionItemRequestDomainDto = item,
                    // Don't update amounts until the last item to avoid multiple calculations
                    withoutUpdateAmounts = index < subscriptionDomainDto.items.size - 1,
                )
            }
        }

        return createdSubscription
    }

    suspend fun updateSubscription(
        tenantId: UUID,
        modifiedBy: UUID,
        subscriptionKey: String,
        subscriptionDomainDto: SubscriptionDomainDto,
    ): Subscription {
        val subscription = subscriptionLoadService.findByTenantIdAndKey(tenantId, subscriptionKey)

        return r2dbcEntityTemplate.update(
            subscription.copy(
                name = subscriptionDomainDto.name,
                description = subscriptionDomainDto.description,
                status = subscriptionDomainDto.status,
                frequency = subscriptionDomainDto.frequency,
                amount = subscriptionDomainDto.amount,
                endDate = subscriptionDomainDto.endDate,
                nextBillingDate = subscriptionDomainDto.nextBillingDate,
                version = subscriptionDomainDto.version,
                lastModifiedBy = modifiedBy,
            ),
        ).awaitSingle()
    }

    suspend fun deleteSubscription(
        tenantId: UUID,
        subscriptionKey: String,
    ) {
        val subscription = subscriptionLoadService.findByTenantIdAndKey(tenantId, subscriptionKey)
        r2dbcEntityTemplate.delete(subscription).awaitSingle()
    }

    suspend fun updateSubscriptionProcessingStatus(
        lastModifiedBy: UUID,
        subscriptionId: UUID,
        processingStatus: ProcessingStatus,
        additionalInformation: String? = null,
    ) {
        val subscriptionToUpdate = subscriptionLoadService.findById(subscriptionId)

        r2dbcEntityTemplate.update(
            subscriptionToUpdate.copy(
                lastModifiedBy = lastModifiedBy,
                processingStatus = processingStatus,
            ),
        ).awaitSingle()

        val subscriptionToUpdateNew = subscriptionLoadService.findById(subscriptionId)

        // Create history entry
        createSubscriptionHistoryEntry(
            subscriptionToUpdate = subscriptionToUpdateNew,
            processingStatus = processingStatus,
            createdBy = lastModifiedBy,
            additionalInformation = additionalInformation,
        )

        logger.info { "Updated subscription $subscriptionId processing status to $processingStatus" }
    }

    suspend fun updateSubscriptionStatus(
        lastModifiedBy: UUID,
        subscriptionId: UUID,
        status: SubscriptionStatus,
    ): Subscription {
        val subscriptionToUpdate = subscriptionLoadService.findById(subscriptionId)

        val updatedSubscription = r2dbcEntityTemplate.update(
            subscriptionToUpdate.copy(
                status = status,
                lastModifiedBy = lastModifiedBy,
            ),
        ).awaitSingle()

        logger.info { "Updated subscription $subscriptionId status to $status" }
        return updatedSubscription
    }

    suspend fun updateSubscriptionBillingProcessed(
        lastModifiedBy: UUID,
        subscriptionId: UUID,
        nextBillingDate: LocalDate,
        additionalInformation: String? = null,
    ): Subscription {
        val subscriptionToUpdate = subscriptionLoadService.findById(subscriptionId)

        r2dbcEntityTemplate.update(
            subscriptionToUpdate.copy(
                nextBillingDate = nextBillingDate,
                processingStatus = ProcessingStatus.SUBSCRIPTION_BILLING_PROCESSED,
                lastModifiedBy = lastModifiedBy,
            ),
        ).awaitSingle()

        val subscriptionToUpdateNew = subscriptionLoadService.findById(subscriptionId)

        // Create history entry
        createSubscriptionHistoryEntry(
            subscriptionToUpdate = subscriptionToUpdateNew,
            processingStatus = ProcessingStatus.SUBSCRIPTION_BILLING_PROCESSED,
            createdBy = lastModifiedBy,
            additionalInformation = additionalInformation,
        )

        logger.info {
            "Updated subscription $subscriptionId processing status to " +
                "SUBSCRIPTION_BILLING_PROCESSED and next billing date to $nextBillingDate"
        }
        return subscriptionToUpdateNew
    }

    suspend fun updateSubscriptionStatusAndProcessingStatus(
        lastModifiedBy: UUID,
        subscriptionId: UUID,
        status: SubscriptionStatus,
        processingStatus: ProcessingStatus,
        additionalInformation: String? = null,
    ): Subscription {
        val subscriptionToUpdate = subscriptionLoadService.findById(subscriptionId)

        r2dbcEntityTemplate.update(
            subscriptionToUpdate.copy(
                status = status,
                processingStatus = processingStatus,
                lastModifiedBy = lastModifiedBy,
            ),
        ).awaitSingle()

        val subscriptionToUpdateNew = subscriptionLoadService.findById(subscriptionId)

        // Create history entry
        createSubscriptionHistoryEntry(
            subscriptionToUpdate = subscriptionToUpdateNew,
            processingStatus = processingStatus,
            createdBy = lastModifiedBy,
            additionalInformation = additionalInformation,
        )

        logger.info { "Updated subscription $subscriptionId status to $status and processing status to $processingStatus" }
        return subscriptionToUpdateNew
    }

    suspend fun updateSubscriptionForBalanceCaseInit(
        lastModifiedBy: UUID,
        subscription: Subscription,
    ): Subscription {
        val subscriptionToUpdate = subscriptionLoadService.findById(subscription.id)

        return r2dbcEntityTemplate.update(
            subscriptionToUpdate.copy(
                lastModifiedBy = lastModifiedBy,
                balanceCaseId = subscription.balanceCaseId,
                version = subscription.version,
            ),
        ).awaitSingle()
    }

    private suspend fun createSubscriptionHistoryEntry(
        subscriptionToUpdate: Subscription,
        processingStatus: ProcessingStatus,
        createdBy: UUID,
        additionalInformation: String?,
    ) {
        val maxNumber = subscriptionHistoryEntryLoadService.findMaxNumberBySubscriptionId(subscriptionToUpdate.id)
        val nextNumber = maxNumber + 1

        val historyEntry = SubscriptionHistoryEntry(
            number = nextNumber,
            subscriptionId = subscriptionToUpdate.id,
            processingStatus = processingStatus,
            additionalInformation = additionalInformation,
            tenantId = subscriptionToUpdate.tenantId,
            createdBy = createdBy,
        )

        subscriptionHistoryService.createHistoryEntry(historyEntry)
    }
}
