package com.klosesoft.billingsolution.domain.logic.service

import com.klosesoft.billingsolution.domain.logic.service.common.SubscriptionService
import com.klosesoft.billingsolution.domain.model.valueobject.Feature
import com.klosesoft.billingsolution.persistence.model.entity.Document
import com.klosesoft.billingsolution.persistence.model.entity.PaymentAssignment
import com.klosesoft.billingsolution.persistence.model.entity.Subscription
import com.klosesoft.billingsolution.persistence.service.TenantLoadService
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class SubscriptionBalanceCaseService(
    private val subscriptionService: SubscriptionService,
    private val balanceCaseService: BalanceCaseService,
    private val tenantLoadService: TenantLoadService,
) {
    suspend fun addBalanceCaseToSubscription(
        subscription: Subscription,
        createdBy: UUID,
        document: Document,
    ) {
        if (!tenantLoadService.findTenantConfigByTenantId(subscription.tenantId).features.contains(Feature.ENABLE_BALANCE_CASE)) {
            return
        }

        if (subscription.balanceCaseId == null) {
            initBalanceCase(subscription, createdBy)
        }

        balanceCaseService.addBalanceCaseItem(subscription.tenantId, createdBy, subscription, document)
    }

    suspend fun addBalanceCaseToSubscription(
        subscription: Subscription,
        createdBy: UUID,
        paymentAssignment: PaymentAssignment,
    ) {
        if (!tenantLoadService.findTenantConfigByTenantId(subscription.tenantId).features.contains(Feature.ENABLE_BALANCE_CASE)) {
            return
        }

        if (subscription.balanceCaseId == null) {
            initBalanceCase(subscription, createdBy)
        }

        balanceCaseService.addBalanceCaseItem(subscription.tenantId, createdBy, subscription, paymentAssignment)
    }

    private suspend fun initBalanceCase(
        subscription: Subscription,
        createdBy: UUID,
    ) {
        val balanceCase = balanceCaseService.createBalanceCase(subscription.tenantId, createdBy, subscription.currency)
        subscription.balanceCaseId = balanceCase.id

        subscriptionService.updateSubscriptionForBalanceCaseInit(createdBy, subscription)
    }
}
